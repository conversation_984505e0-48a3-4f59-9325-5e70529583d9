# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# translations
locales copy

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env
.env.local
.env.development.local
.env.test.local
.env.production
.env.production.local
.env.vercel
# Keep example env file
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts