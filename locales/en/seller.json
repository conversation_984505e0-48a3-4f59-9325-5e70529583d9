{"sellerPortal": "<PERSON><PERSON>", "dashboard": "Dashboard", "products": "Products", "inventory": "Inventory", "orders": "Orders", "shop": "Shop", "customers": "Customers", "analytics": "Analytics", "settings": "Settings", "viewStore": "View Store", "search": "Search...", "notifications": "Notifications", "userMenu": "User menu", "myAccount": "My Account", "profile": "Profile", "logout": "Logout", "manageYourProducts": "Manage your products and inventory", "addProduct": "Add Product", "productInventory": "Product Inventory", "totalProducts": "You have {{count}} products in your inventory.", "searchProducts": "Search products...", "category": "Category", "allCategories": "All Categories", "status": "Status", "allStatus": "All Status", "inStock": "In Stock", "outOfStock": "Out of Stock", "product": "Product", "price": "Price", "stock": "Stock", "actions": "Actions", "noProductsFound": "No products found", "openMenu": "Open menu", "edit": "Edit", "duplicate": "Duplicate", "delete": "Delete", "back": "Back", "error": "Error", "errorLoadingProduct": "Error Loading Product", "productNotFound": "Product not found", "backToProducts": "Back to Products", "editProduct": "Edit Product", "manageVariants": "Manage Variants", "productVariants": "Product Variants", "addVariant": "<PERSON><PERSON>", "editVariant": "<PERSON>", "deleteVariant": "Delete Variant", "variantName": "Variant Name", "attributes": "Attributes", "addAttribute": "Add Attribute", "currentAttributes": "Current Attributes", "noVariantsFound": "No variants found", "createVariant": "Create a new variant for this product with different attributes, price, and stock.", "updateVariant": "Update the details for this product variant.", "deleteVariantConfirm": "This will permanently delete the variant. This action cannot be undone."}