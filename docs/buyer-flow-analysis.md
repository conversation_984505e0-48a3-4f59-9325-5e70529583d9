# Phân tích toàn diện luồng người mua trong dự án ZenBuy

## 1. <PERSON><PERSON><PERSON> tra giao diện người dùng (UI)

### 1.1. <PERSON><PERSON><PERSON> gi<PERSON> các màn hình trong luồng người mua

#### 1.1.1. <PERSON><PERSON> chủ (Landing Page)
- **C<PERSON>u trúc**: <PERSON><PERSON>, Hero section, CategoryCarousel, FeaturedProducts, AllProducts và Footer
- **Thành phần chính**:
  - Hero section với video nền và nút "Shop Now"
  - Carousel hiển thị các danh mục sản phẩm
  - Phần sản phẩm nổi bật hiển thị các sản phẩm có rating cao
  - Danh sách tất cả sản phẩm

#### 1.1.2. <PERSON><PERSON><PERSON> kiếm sản phẩm (Search Page)
- **<PERSON><PERSON><PERSON> trúc**: <PERSON><PERSON> gồm SearchHero và SearchResults
- **Thành phần chính**:
  - Hero section với tiêu đề và hình nền
  - <PERSON><PERSON> s<PERSON>n phẩm (da<PERSON> m<PERSON>, gi<PERSON>, thương hiệu)
  - <PERSON>h sách sản phẩm hi<PERSON>n thị dạng lưới

#### 1.1.3. Chi tiết sản phẩm (Product Detail Page)
- **Cấu trúc**: Hiển thị thông tin chi tiết về sản phẩm
- **Thành phần chính**:
  - Breadcrumb navigation
  - Hình ảnh sản phẩm với hiệu ứng tilt
  - Thông tin sản phẩm (tên, giá, đánh giá, số lượng)
  - Nút "Thêm vào giỏ hàng"
  - Mô tả sản phẩm
  - Đánh giá sản phẩm
  - Thông tin cửa hàng
  - Sản phẩm tương tự

#### 1.1.4. Giỏ hàng (Cart Page)
- **Cấu trúc**: Hiển thị sản phẩm trong giỏ hàng và tổng giá trị
- **Thành phần chính**:
  - Danh sách sản phẩm trong giỏ hàng (CartItemsConnected)
  - Tóm tắt giỏ hàng (CartSummaryConnected) với tổng tiền
  - Sản phẩm đề xuất (RecommendedProducts)

#### 1.1.5. Thanh toán (Checkout Page)
- **Cấu trúc**: Form nhập thông tin giao hàng và phương thức thanh toán
- **Thành phần chính**:
  - Form địa chỉ giao hàng (họ tên, địa chỉ, thành phố, tỉnh/thành, quốc gia, mã bưu điện, số điện thoại)
  - Lựa chọn phương thức thanh toán (thẻ tín dụng, chuyển khoản, tiền mặt)
  - Tóm tắt đơn hàng với danh sách sản phẩm và tổng tiền
  - Nút "Đặt hàng"

#### 1.1.6. Đơn hàng (Orders Page)
- **Cấu trúc**: Hiển thị danh sách đơn hàng và trạng thái
- **Thành phần chính**:
  - Thông tin người dùng (UserProfile)
  - Bộ lọc trạng thái đơn hàng (OrderStatus)
  - Danh sách đơn hàng (OrderListConnected) với thông tin ID, ngày, trạng thái, tổng tiền

#### 1.1.7. Chi tiết đơn hàng (Order Detail Page)
- **Cấu trúc**: Hiển thị thông tin chi tiết về một đơn hàng
- **Thành phần chính**:
  - Thông tin đơn hàng (ID, ngày, trạng thái)
  - Danh sách sản phẩm trong đơn hàng
  - Thông tin giao hàng
  - Thông tin thanh toán
  - Tổng tiền đơn hàng
  - Nút hủy đơn hàng (nếu đơn hàng đang ở trạng thái "pending")

### 1.2. Xác định các vấn đề về UI/UX

#### 1.2.1. Vấn đề về bố cục
- **Trang chủ**: Không có vấn đề lớn, bố cục rõ ràng với các phần được phân chia hợp lý
- **Trang tìm kiếm**: Bộ lọc có thể bị giới hạn, không có tùy chọn sắp xếp kết quả
- **Trang chi tiết sản phẩm**: Có thể cải thiện bằng cách thêm gallery hình ảnh sản phẩm
- **Giỏ hàng**: Không có tùy chọn lưu giỏ hàng hoặc thêm vào danh sách yêu thích

#### 1.2.2. Vấn đề về màu sắc và tính nhất quán
- Sử dụng Tailwind CSS và Shadcn UI giúp duy trì tính nhất quán về màu sắc và giao diện
- Có hỗ trợ chế độ sáng/tối thông qua ThemeChanger
- Không thấy vấn đề lớn về màu sắc và tính nhất quán

#### 1.2.3. Vấn đề về khả năng sử dụng
- **Đăng nhập/Đăng ký**: Không có tùy chọn đăng nhập bằng mạng xã hội
- **Trang chi tiết sản phẩm**: Không có chức năng zoom hình ảnh sản phẩm
- **Giỏ hàng**: Không có tính năng lưu giỏ hàng cho lần sau
- **Thanh toán**: Không có tích hợp với các cổng thanh toán thực tế

### 1.3. Màn hình còn thiếu trong luồng người dùng

1. **Trang hồ sơ người dùng**: Không thấy trang quản lý thông tin cá nhân, địa chỉ giao hàng
2. **Trang đăng nhập/đăng ký**: Cần cải thiện UI, hiện tại chưa đẹp và chưa thân thiện
3. **Trang đánh giá sản phẩm**: Không có form riêng để viết đánh giá sản phẩm
4. **Trang theo dõi đơn hàng**: Không có trang theo dõi trạng thái đơn hàng theo thời gian thực

### 1.4. Vấn đề về điều hướng

1. **Điều hướng cho màn hình cần đăng nhập**: Chưa có cơ chế chuyển hướng người dùng đến trang đăng nhập khi truy cập các trang yêu cầu xác thực
2. **Điều hướng sau đăng nhập**: Chưa có cơ chế chuyển hướng người dùng về trang trước đó sau khi đăng nhập thành công
3. **Điều hướng giữa các màn hình**: Thiếu các liên kết giữa các màn hình liên quan (ví dụ: từ giỏ hàng đến sản phẩm)

## 2. Kiểm tra logic nghiệp vụ

### 2.1. Phân tích luồng hoạt động của người mua

#### 2.1.1. Đăng ký/Đăng nhập
- **Luồng đăng ký**:
  - Người dùng điền thông tin (họ tên, email, mật khẩu)
  - Gửi request đến API `/auth/register`
  - Lưu thông tin người dùng vào database
  - Tạo JWT token và lưu vào cookie
  - Chuyển hướng đến trang chủ

- **Luồng đăng nhập**:
  - Người dùng điền thông tin (email, mật khẩu)
  - Gửi request đến API `/auth/login`
  - Xác thực thông tin đăng nhập
  - Tạo JWT token và lưu vào cookie
  - Chuyển hướng đến trang chủ

#### 2.1.2. Duyệt sản phẩm
- **Luồng xem sản phẩm trên trang chủ**:
  - Hiển thị sản phẩm nổi bật và danh mục
  - Gọi API `/products/featured` để lấy sản phẩm nổi bật
  - Gọi API `/products` để lấy tất cả sản phẩm

- **Luồng tìm kiếm sản phẩm**:
  - Người dùng nhập từ khóa tìm kiếm
  - Gọi API `/products/search?query=...` để lấy kết quả
  - Hiển thị kết quả tìm kiếm
  - Người dùng có thể lọc theo danh mục, giá, thương hiệu

#### 2.1.3. Xem chi tiết sản phẩm
- **Luồng xem chi tiết**:
  - Người dùng chọn sản phẩm từ danh sách
  - Gọi API `/products/{id}` để lấy thông tin chi tiết
  - Hiển thị thông tin sản phẩm, hình ảnh, mô tả, đánh giá
  - Hiển thị thông tin cửa hàng và sản phẩm tương tự

#### 2.1.4. Thêm vào giỏ hàng
- **Luồng thêm sản phẩm**:
  - Người dùng chọn số lượng và nhấn "Thêm vào giỏ hàng"
  - Gọi API `/cart/items` với method POST
  - Cập nhật state giỏ hàng
  - Hiển thị thông báo thành công

- **Luồng cập nhật giỏ hàng**:
  - Người dùng thay đổi số lượng sản phẩm trong giỏ hàng
  - Gọi API `/cart/items/{id}` với method PUT
  - Cập nhật state giỏ hàng

- **Luồng xóa sản phẩm khỏi giỏ hàng**:
  - Người dùng nhấn nút xóa
  - Gọi API `/cart/items/{id}` với method DELETE
  - Cập nhật state giỏ hàng

#### 2.1.5. Thanh toán
- **Luồng thanh toán**:
  - Người dùng điền thông tin giao hàng
  - Chọn phương thức thanh toán
  - Nhấn nút "Đặt hàng"
  - Gọi API `/orders` với method POST
  - Chuyển hướng đến trang đơn hàng

#### 2.1.6. Theo dõi đơn hàng
- **Luồng xem danh sách đơn hàng**:
  - Người dùng truy cập trang đơn hàng
  - Gọi API `/orders` để lấy danh sách đơn hàng
  - Hiển thị danh sách đơn hàng với trạng thái

- **Luồng xem chi tiết đơn hàng**:
  - Người dùng chọn một đơn hàng từ danh sách
  - Gọi API `/orders/{id}` để lấy thông tin chi tiết
  - Hiển thị thông tin chi tiết đơn hàng

### 2.2. Xác định các lỗ hổng hoặc thiếu sót trong logic xử lý

1. **Xác thực người dùng**:
   - Không thấy xử lý token hết hạn
   - Không có cơ chế refresh token
   - Không có xử lý chuyển hướng khi người dùng chưa đăng nhập truy cập trang yêu cầu xác thực
   - Không có cơ chế ghi nhớ URL trước khi chuyển hướng đến trang đăng nhập

2. **Giỏ hàng**:
   - Không có xử lý khi sản phẩm hết hàng
   - Không có cơ chế lưu giỏ hàng cho người dùng chưa đăng nhập
   - Không có xử lý khi thêm sản phẩm vượt quá số lượng tồn kho

3. **Thanh toán**:
   - Không có hệ thống thanh toán nội bộ hoàn chỉnh
   - Không có xử lý khi thanh toán thất bại
   - Không có xác nhận đơn hàng qua email

4. **Đơn hàng**:
   - Không có cơ chế theo dõi đơn hàng theo thời gian thực
   - Không có thông báo khi trạng thái đơn hàng thay đổi
   - Không có xử lý khi người dùng hủy đơn hàng

### 2.3. Đánh giá tính đầy đủ của các chức năng

#### 2.3.1. Chức năng đã hoàn thiện
- Đăng ký/đăng nhập cơ bản
- Xem danh sách sản phẩm và tìm kiếm
- Xem chi tiết sản phẩm
- Thêm sản phẩm vào giỏ hàng
- Thanh toán cơ bản
- Xem danh sách đơn hàng

#### 2.3.2. Chức năng còn thiếu hoặc chưa hoàn thiện
- Quản lý hồ sơ người dùng
- Cải thiện UI trang đăng nhập/đăng ký
- Đánh giá sản phẩm
- Hệ thống thanh toán nội bộ hoàn chỉnh
- Theo dõi đơn hàng theo thời gian thực
- Thông báo trạng thái đơn hàng
- Cơ chế điều hướng cho các trang yêu cầu đăng nhập

## 3. Kiểm tra API

### 3.1. Liệt kê tất cả các API hiện có phục vụ luồng người mua

#### 3.1.1. API Xác thực
- **POST /api/auth/register**: Đăng ký tài khoản mới
- **POST /api/auth/login**: Đăng nhập
- **POST /api/auth/logout**: Đăng xuất
- **GET /api/auth/profile**: Lấy thông tin người dùng
- **PUT /api/auth/profile**: Cập nhật thông tin người dùng
- **GET /api/auth/check**: Kiểm tra trạng thái xác thực

#### 3.1.2. API Sản phẩm
- **GET /api/products**: Lấy danh sách sản phẩm
- **GET /api/products/{id}**: Lấy chi tiết sản phẩm
- **GET /api/products/featured**: Lấy sản phẩm nổi bật
- **GET /api/products/search**: Tìm kiếm sản phẩm

#### 3.1.3. API Giỏ hàng
- **GET /api/cart**: Lấy thông tin giỏ hàng
- **POST /api/cart/items**: Thêm sản phẩm vào giỏ hàng
- **PUT /api/cart/items/{id}**: Cập nhật sản phẩm trong giỏ hàng
- **DELETE /api/cart/items/{id}**: Xóa sản phẩm khỏi giỏ hàng
- **POST /api/cart/clear**: Xóa toàn bộ giỏ hàng

#### 3.1.4. API Đơn hàng
- **GET /api/orders**: Lấy danh sách đơn hàng
- **GET /api/orders/{id}**: Lấy chi tiết đơn hàng
- **POST /api/orders**: Tạo đơn hàng mới
- **PUT /api/orders/{id}**: Cập nhật trạng thái đơn hàng
- **POST /api/orders/{id}/cancel**: Hủy đơn hàng

### 3.2. Xác định các API còn thiếu hoặc chưa được triển khai

1. **API Đánh giá sản phẩm**:
   - Không có API để thêm/sửa/xóa đánh giá sản phẩm

2. **API Hồ sơ người dùng**:
   - Không có API để quản lý địa chỉ giao hàng
   - Không có API để cập nhật thông tin cá nhân đầy đủ

3. **API Thanh toán nội bộ**:
   - Không có API xử lý thanh toán nội bộ hoàn chỉnh
   - Không có API xác nhận thanh toán

4. **API Thông báo**:
   - Không có API để lấy/đánh dấu đã đọc thông báo

5. **API Xác thực nâng cao**:
   - Không có API refresh token
   - Không có API kiểm tra trạng thái đăng nhập chi tiết

### 3.3. Kiểm tra các API đã có nhưng chưa được tích hợp vào frontend

1. **API Cập nhật trạng thái đơn hàng**:
   - Đã có API nhưng chưa thấy tích hợp vào giao diện người dùng

2. **API Hủy đơn hàng**:
   - Đã có API nhưng chưa thấy tích hợp đầy đủ vào giao diện

3. **API Xóa toàn bộ giỏ hàng**:
   - Đã có API nhưng chưa thấy nút xóa toàn bộ giỏ hàng trên giao diện

## 4. Đề xuất cải tiến

### 4.1. Đề xuất cải tiến UI/UX (ưu tiên theo thứ tự)

1. **Trang đăng nhập/đăng ký**:
   - Cải thiện giao diện với thiết kế hiện đại hơn
   - Thêm xác thực form đầy đủ với thông báo lỗi rõ ràng
   - Thêm tùy chọn "Nhớ mật khẩu"
   - Cải thiện responsive trên các thiết bị

2. **Cơ chế điều hướng**:
   - Thêm cơ chế chuyển hướng đến trang đăng nhập khi truy cập trang yêu cầu xác thực
   - Thêm cơ chế ghi nhớ URL trước khi chuyển hướng đến trang đăng nhập
   - Cải thiện breadcrumb navigation

3. **Trang chi tiết sản phẩm**:
   - Thêm gallery hình ảnh với chức năng zoom cơ bản
   - Cải thiện hiển thị thông tin sản phẩm
   - Thêm tab "Thông số kỹ thuật"

4. **Giỏ hàng**:
   - Thêm tùy chọn "Xóa tất cả"
   - Cải thiện hiển thị thông tin sản phẩm trong giỏ hàng
   - Thêm xác nhận khi xóa sản phẩm

5. **Thanh toán**:
   - Cải thiện form địa chỉ giao hàng
   - Thêm tùy chọn lưu địa chỉ giao hàng
   - Cải thiện hiển thị tóm tắt đơn hàng

6. **Đơn hàng**:
   - Thêm timeline theo dõi trạng thái đơn hàng cơ bản
   - Cải thiện hiển thị thông tin đơn hàng
   - Thêm tùy chọn hủy đơn hàng rõ ràng

### 4.2. Đề xuất API cần phát triển hoặc hoàn thiện (ưu tiên theo thứ tự)

1. **API Xác thực nâng cao**:
   - `POST /api/auth/refresh-token`: Làm mới token
   - `GET /api/auth/status`: Kiểm tra trạng thái đăng nhập chi tiết
   - `POST /api/auth/logout-all`: Đăng xuất trên tất cả thiết bị

2. **API Hồ sơ người dùng**:
   - `GET /api/user/addresses`: Lấy danh sách địa chỉ giao hàng
   - `POST /api/user/addresses`: Thêm địa chỉ giao hàng mới
   - `PUT /api/user/addresses/{id}`: Cập nhật địa chỉ giao hàng
   - `DELETE /api/user/addresses/{id}`: Xóa địa chỉ giao hàng

3. **API Thanh toán nội bộ**:
   - `POST /api/payments/process`: Xử lý thanh toán nội bộ
   - `GET /api/payments/{id}/status`: Kiểm tra trạng thái thanh toán
   - `POST /api/payments/{id}/confirm`: Xác nhận thanh toán

4. **API Đánh giá sản phẩm**:
   - `GET /api/products/{id}/reviews`: Lấy đánh giá của sản phẩm
   - `POST /api/products/{id}/reviews`: Thêm đánh giá mới
   - `PUT /api/products/{id}/reviews/{reviewId}`: Cập nhật đánh giá
   - `DELETE /api/products/{id}/reviews/{reviewId}`: Xóa đánh giá

5. **API Thông báo**:
   - `GET /api/notifications`: Lấy danh sách thông báo
   - `PUT /api/notifications/{id}/read`: Đánh dấu thông báo đã đọc
   - `PUT /api/notifications/read-all`: Đánh dấu tất cả thông báo đã đọc

### 4.3. Đề xuất màn hình mới cần bổ sung (ưu tiên theo thứ tự)

1. **Trang hồ sơ người dùng**:
   - Thông tin cá nhân (họ tên, email, số điện thoại)
   - Quản lý địa chỉ giao hàng
   - Thay đổi mật khẩu
   - Lịch sử đơn hàng

2. **Trang đăng nhập/đăng ký cải tiến**:
   - Giao diện hiện đại
   - Xác thực form đầy đủ
   - Tùy chọn nhớ mật khẩu
   - Responsive trên các thiết bị

3. **Trang theo dõi đơn hàng chi tiết**:
   - Timeline trạng thái đơn hàng
   - Thông tin vận chuyển
   - Dự kiến thời gian giao hàng

4. **Trang đánh giá sản phẩm**:
   - Form đánh giá với rating và nội dung
   - Tùy chọn thêm hình ảnh

### 4.4. Lộ trình ưu tiên cho việc phát triển tiếp theo

#### Giai đoạn 1: Cải thiện xác thực và điều hướng (1-2 tuần)
1. Cải thiện UI trang đăng nhập/đăng ký
2. Thêm cơ chế điều hướng cho các trang yêu cầu đăng nhập
3. Thêm cơ chế ghi nhớ URL trước khi chuyển hướng
4. Phát triển API refresh token
5. Cải thiện xác thực form với thông báo lỗi rõ ràng

#### Giai đoạn 2: Hoàn thiện các chức năng cơ bản (2-3 tuần)
1. Phát triển trang hồ sơ người dùng
2. Cải thiện trang chi tiết sản phẩm (gallery hình ảnh cơ bản)
3. Hoàn thiện giỏ hàng (thêm nút xóa tất cả, xác nhận xóa)
4. Cải thiện trang thanh toán (lưu địa chỉ giao hàng)
5. Cải thiện trang đơn hàng (timeline trạng thái cơ bản)

#### Giai đoạn 3: Phát triển hệ thống thanh toán nội bộ (2-3 tuần)
1. Phát triển API thanh toán nội bộ
2. Tích hợp xác nhận đơn hàng qua email
3. Thêm xử lý khi thanh toán thất bại
4. Cải thiện hiển thị thông tin thanh toán
5. Thêm trang theo dõi đơn hàng chi tiết

#### Giai đoạn 4: Bổ sung tính năng đánh giá và thông báo (2-3 tuần)
1. Phát triển hệ thống đánh giá sản phẩm
2. Thêm trang đánh giá sản phẩm
3. Phát triển hệ thống thông báo cơ bản
4. Thêm thông báo khi trạng thái đơn hàng thay đổi
