# Hướng dẫn kiểm thử

Tài liệu này cung cấp hướng dẫn chi tiết để kiểm thử các tính năng đã triển khai trong dự án.

## Lưu ý quan trọng

Để thuận tiện cho việc kiểm thử, chúng tôi đã tạm thời bỏ qua phần xác thực (auth) cho các API của seller. Điều này cho phép bạn kiểm thử các tính năng mà không cần đăng nhập. Trong môi trường sản xuất, các API này sẽ yêu cầu xác thực.

## Sprint 3: Quản lý sản phẩm và kho hàng

### 1. Ki<PERSON>m thử API sản phẩm và kho hàng

#### 1.1. Sử dụng script kiểm thử tự động

Chúng tôi đã tạo hai script kiểm thử tự động để kiểm tra các API sản phẩm và kho hàng:

**Script kiểm thử API cơ bản:**
```bash
pnpm run test:product-apis
```

Script này sẽ thực hiện các bước sau:
- Đăng nhập để lấy token
- Tạo sản phẩm mới
- Nhân bản sản phẩm
- Thêm hình ảnh sản phẩm
- Cập nhật tồn kho hàng loạt
- Cài đặt ngưỡng tồn kho tối thiểu
- Thêm đánh giá sản phẩm
- Cập nhật đánh giá sản phẩm

**Script kiểm thử luồng quản lý sản phẩm và kho hàng đầy đủ:**
```bash
pnpm run test:product-inventory
```

Script này sẽ thực hiện toàn bộ luồng quản lý sản phẩm và kho hàng:
- Đăng nhập để lấy token
- Tạo sản phẩm mới
- Cập nhật sản phẩm
- Thêm hình ảnh sản phẩm
- Cập nhật thứ tự hình ảnh
- Nhân bản sản phẩm
- Cập nhật tồn kho
- Cài đặt ngưỡng tồn kho tối thiểu
- Lấy lịch sử nhập/xuất kho
- Xóa sản phẩm

Script này cũng sẽ ghi log chi tiết vào thư mục `logs/` để bạn có thể xem lại kết quả kiểm thử.

#### 1.2. Kiểm thử thủ công với cURL

Bạn cũng có thể kiểm thử các API bằng cách sử dụng cURL:

**Lấy danh sách sản phẩm:**
```bash
curl -X GET "http://localhost:3001/api/seller/products"
```

**Tạo sản phẩm mới:**
```bash
curl -X POST "http://localhost:3001/api/seller/products" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Product",
    "description": "This is a test product",
    "price": 99.99,
    "stock": 100,
    "specifications": {
      "Color": "Black",
      "Weight": "200g"
    },
    "minStockThreshold": 10
  }'
```

**Nhân bản sản phẩm (thay PRODUCT_ID bằng ID sản phẩm thực tế):**
```bash
curl -X POST "http://localhost:3001/api/seller/products/PRODUCT_ID/duplicate"
```

**Cập nhật tồn kho hàng loạt:**
```bash
curl -X POST "http://localhost:3001/api/seller/inventory/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "items": [
      {
        "productId": "PRODUCT_ID",
        "quantity": 10,
        "actionType": "add",
        "notes": "Thêm hàng từ curl test"
      }
    ]
  }'
```

### 2. Kiểm thử giao diện người dùng

#### 2.1. Trang quản lý sản phẩm (Seller)

**Truy cập trang danh sách sản phẩm:**
1. Đi đến `/seller/products/list`
2. Kiểm tra danh sách sản phẩm hiển thị chính xác
3. Thử tìm kiếm sản phẩm bằng cách nhập từ khóa vào ô tìm kiếm
4. Thử sắp xếp sản phẩm theo các tiêu chí khác nhau

**Thêm sản phẩm mới:**
1. Đi đến `/seller/products/add`
2. Điền thông tin cơ bản của sản phẩm ở tab đầu tiên
3. Nhấn "Save & Continue" để chuyển sang tab tiếp theo
4. Thêm hình ảnh sản phẩm ở tab thứ hai
5. Nhấn "Save & Continue" để chuyển sang tab tiếp theo
6. Thêm thông số kỹ thuật ở tab thứ ba
7. Nhấn "Save Specifications" để hoàn tất việc thêm sản phẩm

**Nhân bản sản phẩm:**
1. Đi đến trang danh sách sản phẩm `/seller/products/list`
2. Nhấn vào nút "..." bên cạnh sản phẩm
3. Chọn "Duplicate" từ menu dropdown
4. Kiểm tra xem sản phẩm đã được nhân bản và bạn được chuyển đến trang chỉnh sửa sản phẩm mới

#### 2.2. Trang quản lý kho hàng (Seller)

**Truy cập trang quản lý kho hàng:**
1. Đi đến `/seller/inventory`
2. Kiểm tra tab "Overview" hiển thị tổng quan kho hàng
3. Thử cập nhật tồn kho bằng cách nhấn vào nút "+" hoặc "-" bên cạnh sản phẩm
4. Kiểm tra tab "History" hiển thị lịch sử nhập/xuất kho
5. Kiểm tra tab "Low Stock" hiển thị cảnh báo khi hàng sắp hết
6. Kiểm tra tab "Batch Update" cho phép cập nhật tồn kho hàng loạt

**Cài đặt ngưỡng tồn kho tối thiểu:**
1. Đi đến tab "Low Stock"
2. Nhấn vào nút cài đặt bên cạnh sản phẩm
3. Nhập ngưỡng tồn kho tối thiểu
4. Nhấn "Save Threshold" để cập nhật ngưỡng
5. Kiểm tra xem sản phẩm có xuất hiện trong tab "Low Stock" khi tồn kho dưới ngưỡng không

#### 2.3. Trang chi tiết sản phẩm (Buyer)

**Truy cập trang chi tiết sản phẩm:**
1. Đi đến `/product/PRODUCT_ID` (thay PRODUCT_ID bằng ID sản phẩm thực tế)
2. Kiểm tra gallery hình ảnh hiển thị chính xác
3. Thử nhấn vào hình ảnh để xem ở chế độ lightbox
4. Thử sử dụng chức năng zoom bằng cách nhấn vào hình ảnh
5. Kiểm tra tab "Description" hiển thị mô tả sản phẩm
6. Kiểm tra tab "Specifications" hiển thị thông số kỹ thuật sản phẩm
7. Kiểm tra phần đánh giá sản phẩm hiển thị chính xác

## Khắc phục sự cố

Nếu bạn gặp vấn đề khi kiểm thử, hãy thử các bước sau:

1. Đảm bảo server đang chạy với lệnh `pnpm run dev`
2. Kiểm tra console trong trình duyệt để xem lỗi frontend
3. Kiểm tra console server để xem lỗi backend
4. Đảm bảo MongoDB đang chạy và kết nối thành công
5. Nếu có lỗi xác thực, hãy kiểm tra xem bạn đã đăng nhập chưa (mặc dù chúng tôi đã tạm thời bỏ qua phần xác thực cho các API của seller)

Nếu vẫn gặp vấn đề, hãy liên hệ với đội phát triển để được hỗ trợ.
