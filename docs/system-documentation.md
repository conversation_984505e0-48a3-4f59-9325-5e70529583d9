# Tài liệ<PERSON> thống Zen<PERSON>uy - <PERSON><PERSON> tích <PERSON>à<PERSON> diện

## <PERSON><PERSON><PERSON>

1. [Tổng quan Dự án](#1-tổng-quan-dự-án)
2. [<PERSON><PERSON><PERSON> trú<PERSON> thống](#2-kiến-tr<PERSON><PERSON>-hệ-thống)
3. [<PERSON><PERSON><PERSON> nghệ và <PERSON>h<PERSON> viện](#3-công-nghệ-và-thư-viện)
4. [<PERSON><PERSON><PERSON> trú<PERSON>n](#4-cấu-trúc-dự-án)
5. [<PERSON><PERSON> sở Dữ liệu](#5-cơ-sở-dữ-liệu)
6. [<PERSON> và Backend](#6-api-và-backend)
7. [Frontend và UI](#7-frontend-và-ui)
8. [<PERSON><PERSON><PERSON> thự<PERSON> và <PERSON> mật](#8-xác-thực-và-bảo-mật)
9. [<PERSON><PERSON> ngôn ngữ (i18n)](#9-đa-ngôn-ngữ-i18n)
10. [Triể<PERSON> khai và DevOps](#10-triển-khai-và-devops)
11. [<PERSON><PERSON><PERSON> năng <PERSON>](#11-tính-năng-chính)
12. [Scripts và Utilities](#12-scripts-và-utilities)
13. [Testing và Quality Assurance](#13-testing-và-quality-assurance)
14. [Hướng dẫn Migration](#14-hướng-dẫn-migration)

---

## 1. Tổng quan Dự án

### 1.1 Thông tin Cơ bản
- **Tên dự án**: ZenBuy (ecommerce.io)
- **Phiên bản**: 1.0.0
- **Tác giả**: shadowhira
- **Loại**: Nền tảng thương mại điện tử đa người bán
- **Ngôn ngữ chính**: TypeScript
- **Framework**: Next.js 15.1.0

### 1.2 Mô tả Dự án
ZenBuy là một nền tảng thương mại điện tử toàn diện được xây dựng với Next.js, hỗ trợ:
- **Marketplace đa người bán**: Cho phép nhiều người bán tạo shop và bán hàng
- **Quản lý đơn hàng**: Hệ thống quản lý đơn hàng từ đặt hàng đến giao hàng
- **Phân tích và báo cáo**: Dashboard analytics cho người bán
- **Quản lý kho hàng**: Theo dõi tồn kho, lịch sử nhập/xuất
- **Đa ngôn ngữ**: Hỗ trợ 4 ngôn ngữ (Tiếng Việt, English, 日本語, 中文)
- **Responsive design**: Tối ưu cho mọi thiết bị

### 1.3 Đối tượng Người dùng
1. **Khách hàng (Customer)**: Mua sắm sản phẩm
2. **Người bán (Seller)**: Quản lý shop, sản phẩm, đơn hàng
3. **Quản trị viên (Admin)**: Quản lý toàn bộ hệ thống

---

## 2. Kiến trúc Hệ thống

### 2.1 Kiến trúc Tổng thể
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (Next.js)     │◄──►│   (API Routes)  │◄──►│   (MongoDB)     │
│                 │    │                 │    │                 │
│ - React 18      │    │ - Next.js API   │    │ - MongoDB Atlas │
│ - TypeScript    │    │ - Mongoose ODM  │    │ - Local MongoDB │
│ - Tailwind CSS  │    │ - JWT Auth      │    │                 │
│ - Radix UI      │    │ - Zod Validation│    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 Kiến trúc Frontend
- **Framework**: Next.js 15 với App Router
- **Rendering**: Server-Side Rendering (SSR) + Client-Side Rendering (CSR)
- **State Management**:
  - React Query (@tanstack/react-query) cho server state
  - Hookstate (@hookstate/core) cho local state
- **Routing**: File-based routing với layout groups
- **Styling**: Tailwind CSS + Radix UI components

### 2.3 Kiến trúc Backend
- **API**: Next.js API Routes (App Router)
- **Database**: MongoDB với Mongoose ODM
- **Authentication**: JWT tokens với cookie storage
- **Validation**: Zod schemas
- **File Upload**: Local file system với UUID naming

### 2.4 Cấu trúc Thư mục Chính
```
src/
├── app/                 # Next.js App Router
│   ├── [locale]/       # Internationalization routes
│   ├── api/            # API endpoints
│   ├── globals.css     # Global styles
│   └── layout.tsx      # Root layout
├── components/         # React components
├── lib/               # Utility libraries
├── models/            # MongoDB models
├── types/             # TypeScript type definitions
└── styles/            # Additional styles
```

---

## 3. Công nghệ và Thư viện

### 3.1 Core Technologies
| Công nghệ | Phiên bản | Mục đích |
|-----------|-----------|----------|
| **Next.js** | 15.1.0 | React framework với SSR/SSG |
| **React** | 18.3.1 | UI library |
| **TypeScript** | 5.7.2 | Type safety |
| **MongoDB** | 6.15.0 | NoSQL database |
| **Mongoose** | 8.13.1 | MongoDB ODM |

### 3.2 UI và Styling
| Thư viện | Phiên bản | Mục đích |
|----------|-----------|----------|
| **Tailwind CSS** | 3.4.17 | Utility-first CSS framework |
| **Radix UI** | Various | Headless UI components |
| **Lucide React** | 0.468.0 | Icon library |
| **Framer Motion** | 11.18.2 | Animation library |
| **Chart.js** | 4.4.8 | Data visualization |
| **Recharts** | 2.15.1 | React charts |

### 3.3 Form và Validation
| Thư viện | Phiên bản | Mục đích |
|----------|-----------|----------|
| **React Hook Form** | 7.54.2 | Form management |
| **Zod** | 3.24.2 | Schema validation |
| **@hookform/resolvers** | 3.10.0 | Form validation integration |

### 3.4 State Management
| Thư viện | Phiên bản | Mục đích |
|----------|-----------|----------|
| **@tanstack/react-query** | 5.67.2 | Server state management |
| **@hookstate/core** | 4.0.1 | Local state management |

### 3.5 Authentication và Security
| Thư viện | Phiên bản | Mục đích |
|----------|-----------|----------|
| **jsonwebtoken** | 9.0.2 | JWT token generation/verification |
| **bcryptjs** | 3.0.2 | Password hashing |
| **next-auth** | 5.0.0-beta.25 | Authentication framework |

### 3.6 Internationalization
| Thư viện | Phiên bản | Mục đích |
|----------|-----------|----------|
| **next-i18n-router** | 5.5.1 | i18n routing |
| **react-i18next** | 15.4.1 | React i18n |
| **i18next** | 24.2.2 | Core i18n library |

### 3.7 Development Tools
| Tool | Phiên bản | Mục đích |
|------|-----------|----------|
| **ESLint** | 9.22.0 | Code linting |
| **Prettier** | 3.4.2 | Code formatting |
| **Husky** | 9.1.7 | Git hooks |
| **TypeScript ESLint** | 8.26.0 | TypeScript linting |

### 3.8 Utilities và Helpers
| Thư viện | Phiên bản | Mục đích |
|----------|-----------|----------|
| **@faker-js/faker** | 9.6.0 | Generate fake data |
| **uuid** | 11.1.0 | UUID generation |
| **date-fns** | 4.1.0 | Date manipulation |
| **sharp** | 0.33.5 | Image processing |
| **qrcode** | 1.5.4 | QR code generation |
| **jsbarcode** | 3.11.6 | Barcode generation |

---

## 4. Cấu trúc Dự án

### 4.1 Cấu trúc Thư mục Chi tiết
```
zen-buy-fe/
├── docs/                           # Tài liệu dự án
│   ├── buyer-flow-analysis.md
│   ├── implementation-plan.md
│   ├── progress-tracking.md
│   ├── seller-flow-analysis.md
│   └── testing-guide.md
├── locales/                        # Đa ngôn ngữ
│   ├── en/                        # English
│   ├── vi/                        # Tiếng Việt
│   ├── ja/                        # 日本語
│   └── zh/                        # 中文
├── public/                         # Static assets
│   ├── images/
│   ├── svg/
│   ├── uploads/                   # User uploaded files
│   └── videos/
├── scripts/                        # Utility scripts
│   ├── migrate-to-atlas.js
│   ├── seed-rich-data.js
│   └── test-product-apis.js
├── src/
│   ├── app/                       # Next.js App Router
│   │   ├── [locale]/             # Internationalized routes
│   │   │   ├── (client-layout)/  # Client pages layout
│   │   │   ├── (seller-layout)/  # Seller pages layout
│   │   │   └── (no-layout)/      # Auth pages
│   │   ├── api/                  # API endpoints
│   │   ├── hooks/                # Custom React hooks
│   │   ├── services/             # API service layers
│   │   └── store/                # State management
│   ├── components/               # React components
│   │   ├── auth/                 # Authentication components
│   │   ├── cart/                 # Shopping cart
│   │   ├── checkout/             # Checkout process
│   │   ├── landing/              # Landing page
│   │   ├── layout/               # Layout components
│   │   ├── orders/               # Order management
│   │   ├── product/              # Product display
│   │   ├── seller/               # Seller dashboard
│   │   ├── shop/                 # Shop pages
│   │   └── ui/                   # Reusable UI components
│   ├── lib/                      # Utility libraries
│   ├── models/                   # MongoDB models
│   ├── styles/                   # Additional styles
│   └── types/                    # TypeScript definitions
├── .env.local                    # Environment variables
├── next.config.mjs              # Next.js configuration
├── tailwind.config.ts           # Tailwind CSS config
├── tsconfig.json                # TypeScript config
└── package.json                 # Dependencies
```

### 4.2 Layout Groups trong App Router
```
app/[locale]/
├── (client-layout)/              # Layout cho khách hàng
│   ├── layout.tsx               # Navbar + Footer
│   ├── cart/
│   ├── checkout/
│   ├── orders/
│   ├── product/
│   ├── profile/
│   ├── search/
│   └── shop/
├── (seller-layout)/              # Layout cho người bán
│   ├── layout.tsx               # Seller sidebar
│   └── seller/
│       ├── analytics/
│       ├── inventory/
│       ├── orders/
│       ├── products/
│       └── reports/
└── (no-layout)/                  # Không layout (auth)
    ├── login/
    └── register/
```

### 4.3 API Routes Structure
```
app/api/
├── auth/                         # Authentication
│   ├── login/
│   ├── register/
│   ├── logout/
│   ├── profile/
│   └── change-password/
├── products/                     # Product management
│   ├── route.ts                 # GET, POST products
│   ├── [id]/                    # Individual product
│   ├── featured/                # Featured products
│   └── search/                  # Product search
├── orders/                       # Order management
│   ├── route.ts
│   ├── [id]/
│   └── user-history/
├── cart/                         # Shopping cart
│   ├── route.ts
│   ├── items/
│   └── clear/
├── seller/                       # Seller-specific APIs
│   ├── products/
│   ├── orders/
│   ├── analytics/
│   ├── inventory/
│   └── reports/
├── analytics/                    # Analytics data
├── categories/                   # Product categories
├── notifications/                # User notifications
└── upload-image/                 # File upload
```

---

## 5. Cơ sở Dữ liệu

### 5.1 MongoDB Configuration
- **Database**: MongoDB (Local + Atlas)
- **ODM**: Mongoose 8.13.1
- **Connection**: Cached connection pattern cho performance
- **URI**: `mongodb://localhost:27017/zenbuy` (local) hoặc MongoDB Atlas

### 5.2 Database Models

#### 5.2.1 User Model
```typescript
interface IUser {
  name: string;
  email: string;
  password: string;
  role: 'customer' | 'seller' | 'admin';
  avatar?: string;
  phone?: string;
  bio?: string;
  preferences?: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
}

interface UserPreferences {
  newsletter: boolean;
  notifications: boolean;
  orderNotifications: boolean;
  inventoryNotifications: boolean;
  paymentNotifications: boolean;
  systemNotifications: boolean;
  language: string;
}
```

#### 5.2.2 Product Model
```typescript
interface IProduct {
  title: string;
  slug: string;
  description: string;
  price: number;
  discountPrice?: number;
  stock: number;
  images: string[];
  category: ObjectId;
  shop: ObjectId;
  rating: number;
  reviews: number;
  featured: boolean;
  variants?: ProductVariant[];
  specifications?: Record<string, string>;
  minStockThreshold?: number;
}

interface ProductVariant {
  name: string;
  price: number;
  stock: number;
  attributes: Record<string, string>;
}
```

#### 5.2.3 Order Model
```typescript
interface IOrder {
  orderNumber: string;
  user: ObjectId;
  items: OrderItem[];
  totalAmount: number;
  shippingFee: number;
  discount: number;
  tax: number;
  finalAmount: number;
  paymentMethod: 'credit_card' | 'bank_transfer' | 'cash' | 'cod';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  statusHistory: OrderStatus[];
  shippingAddress: ObjectId | Record<string, any>;
  trackingNumber?: string;
}
```

#### 5.2.4 Shop Model
```typescript
interface IShop {
  name: string;
  slug: string;
  description: string;
  logo: string;
  banner: string;
  owner: ObjectId;
  followers: number;
  rating: number;
}
```

#### 5.2.5 Category Model
```typescript
interface ICategory {
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parent?: ObjectId;
}
```

#### 5.2.6 Cart Model
```typescript
interface ICart {
  user: ObjectId;
  items: CartItem[];
  totalAmount: number;
  updatedAt: Date;
}

interface CartItem {
  product: ObjectId;
  quantity: number;
  variant?: string;
  attributes?: Record<string, string>;
}
```

#### 5.2.7 Additional Models
- **Review**: Đánh giá sản phẩm
- **Notification**: Thông báo người dùng
- **InventoryHistory**: Lịch sử nhập/xuất kho
- **ShippingAddress**: Địa chỉ giao hàng

### 5.3 Database Features
- **Auto-generated IDs**: MongoDB ObjectId
- **Timestamps**: Automatic createdAt/updatedAt
- **Indexes**: Optimized queries
- **Validation**: Mongoose schema validation
- **Relationships**: Referenced documents
- **Middleware**: Pre/post hooks for business logic

---

## 6. API và Backend

### 6.1 API Architecture
- **Framework**: Next.js API Routes (App Router)
- **Validation**: Zod schemas
- **Error Handling**: Structured error responses
- **Authentication**: JWT middleware
- **Rate Limiting**: Built-in protection

### 6.2 Authentication System
```typescript
// JWT Token Structure
interface JWTPayload {
  id: string;
  email: string;
  role: 'customer' | 'seller' | 'admin';
  iat: number;
  exp: number;
}

// Authentication Flow
1. Login → Generate JWT → Store in HTTP-only cookie
2. Request → Extract token → Verify → Attach user to request
3. Protected routes → Check role permissions
```

### 6.3 API Endpoints Overview

#### 6.3.1 Authentication APIs
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `POST /api/auth/change-password` - Change password
- `POST /api/auth/refresh-token` - Refresh JWT token

#### 6.3.2 Product APIs
- `GET /api/products` - List products with filters
- `POST /api/products` - Create new product (seller)
- `GET /api/products/[id]` - Get product details
- `PUT /api/products/[id]` - Update product (seller)
- `DELETE /api/products/[id]` - Delete product (seller)
- `GET /api/products/featured` - Get featured products
- `GET /api/products/search` - Search products

#### 6.3.3 Order APIs
- `GET /api/orders` - List user orders
- `POST /api/orders` - Create new order
- `GET /api/orders/[id]` - Get order details
- `PUT /api/orders/[id]` - Update order status
- `GET /api/orders/user-history` - User order history

#### 6.3.4 Cart APIs
- `GET /api/cart` - Get user cart
- `POST /api/cart` - Add item to cart
- `PUT /api/cart` - Update cart item
- `DELETE /api/cart` - Remove cart item
- `POST /api/cart/clear` - Clear entire cart

#### 6.3.5 Seller APIs
- `GET /api/seller/products` - Seller's products
- `GET /api/seller/orders` - Seller's orders
- `GET /api/seller/analytics` - Sales analytics
- `GET /api/seller/inventory` - Inventory management
- `POST /api/seller/inventory/batch` - Batch inventory update
- `GET /api/seller/reports` - Generate reports

### 6.4 API Response Format
```typescript
// Success Response
{
  data: any;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Error Response
{
  error: string;
  message?: string;
  details?: any;
}
```

### 6.5 Middleware System
```typescript
// Authentication Middleware
const protectedApiRoutes = [
  "/api/cart",
  "/api/orders",
  "/api/seller",
  // ...
];

// Role-based Access Control
- Customer: Basic shopping features
- Seller: Shop management + Customer features
- Admin: Full system access
```

---

## 7. Frontend và UI

### 7.1 UI Framework Stack
- **Base**: React 18.3.1 với TypeScript
- **Styling**: Tailwind CSS 3.4.17
- **Components**: Radix UI (Headless components)
- **Icons**: Lucide React 0.468.0
- **Animations**: Framer Motion 11.18.2
- **Charts**: Chart.js 4.4.8 + Recharts 2.15.1

### 7.2 Component Architecture
```
components/
├── ui/                    # Base UI components (Radix + Tailwind)
│   ├── button.tsx
│   ├── input.tsx
│   ├── dialog.tsx
│   ├── table.tsx
│   └── ...
├── layout/               # Layout components
│   ├── navbar.tsx
│   ├── footer.tsx
│   └── sidebar.tsx
├── auth/                 # Authentication
├── cart/                 # Shopping cart
├── checkout/             # Checkout process
├── product/              # Product display
├── seller/               # Seller dashboard
└── utils/                # Utility components
```

### 7.3 State Management Strategy

#### 7.3.1 Server State (React Query)
```typescript
// Product queries
const { data: products } = useQuery({
  queryKey: ['products', filters],
  queryFn: () => fetchProducts(filters)
});

// Mutations
const createProductMutation = useMutation({
  mutationFn: createProduct,
  onSuccess: () => queryClient.invalidateQueries(['products'])
});
```

#### 7.3.2 Local State (Hookstate)
```typescript
// Global state
const authState = hookstate({
  user: null,
  isAuthenticated: false,
  loading: false
});

// Component state
const [formData, setFormData] = useState(initialData);
```

### 7.4 Form Management
```typescript
// React Hook Form + Zod
const form = useForm<FormData>({
  resolver: zodResolver(schema),
  defaultValues: initialValues
});

const onSubmit = (data: FormData) => {
  // Handle form submission
};
```

### 7.5 Responsive Design
- **Mobile-first**: Tailwind CSS breakpoints
- **Breakpoints**: sm (640px), md (768px), lg (1024px), xl (1280px)
- **Components**: Responsive by default
- **Navigation**: Mobile hamburger menu, desktop sidebar

### 7.6 Theme System
```typescript
// Dark/Light mode support
const { theme, setTheme } = useTheme();

// CSS Variables
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  // ...
}

[data-theme="dark"] {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  // ...
}
```

---

## 8. Xác thực và Bảo mật

### 8.1 Authentication Strategy
- **JWT Tokens**: Stateless authentication
- **Cookie Storage**: HTTP-only cookies cho security
- **Token Expiry**: 7 days default, configurable
- **Refresh Mechanism**: Automatic token refresh
- **Role-based Access**: Customer, Seller, Admin roles

### 8.2 Security Features
```typescript
// Password Security
- bcryptjs hashing với salt rounds = 10
- Minimum 6 characters requirement
- Password comparison method

// JWT Security
- Secret key từ environment variables
- HTTP-only cookies
- Secure flag trong production
- SameSite strict policy

// API Security
- Input validation với Zod schemas
- SQL injection protection (MongoDB)
- XSS protection
- CSRF protection với SameSite cookies
```

### 8.3 Middleware Protection
```typescript
// Route Protection
const protectedApiRoutes = [
  "/api/cart",
  "/api/orders",
  "/api/analytics",
  "/api/seller"
];

// Role-based Access Control
if (pathname.startsWith('/api/seller') &&
    decoded.role !== 'seller' &&
    decoded.role !== 'admin') {
  return 403; // Forbidden
}
```

### 8.4 Environment Variables
```bash
# Database
MONGODB_URI=mongodb://localhost:27017/zenbuy

# Authentication
JWT_SECRET=your_jwt_secret_here_change_this_in_production

# Development
NODE_ENV=development
HUSKY=0  # Disable in CI/CD
```

---

## 9. Đa ngôn ngữ (i18n)

### 9.1 i18n Configuration
```javascript
// i18nConfig.js
const i18nConfig = {
  locales: ['en', 'vi', 'ja', 'zh'],
  defaultLocale: 'vi'
};
```

### 9.2 Supported Languages
- **vi**: Tiếng Việt (Default)
- **en**: English
- **ja**: 日本語 (Japanese)
- **zh**: 中文 (Chinese)

### 9.3 Translation Structure
```
locales/
├── en/
│   ├── landing.json
│   ├── navbar-general.json
│   ├── cart.json
│   ├── checkout.json
│   ├── orders.json
│   ├── seller-dashboard.json
│   └── ...
├── vi/
├── ja/
└── zh/
```

### 9.4 i18n Implementation
```typescript
// Route-based i18n
app/[locale]/page.tsx

// Translation usage
import { useTranslation } from 'react-i18next';

const { t } = useTranslation('namespace');
const text = t('key');

// Namespace organization
- landing: Landing page content
- navbar-general: Navigation
- cart: Shopping cart
- seller-*: Seller dashboard sections
```

### 9.5 i18n Tools
- **next-i18n-router**: Route-based i18n
- **react-i18next**: React integration
- **i18nexus-cli**: Translation management

---

## 10. Triển khai và DevOps

### 10.1 Deployment Platform
- **Primary**: Vercel (Automatic deployment)
- **Database**: MongoDB Atlas
- **CDN**: Vercel Edge Network
- **Domain**: Custom domain support

### 10.2 Build Configuration
```javascript
// next.config.mjs
const nextConfig = {
  sassOptions: {
    includePaths: ['./src/styles']
  },
  eslint: {
    ignoreDuringBuilds: true
  },
  typescript: {
    ignoreBuildErrors: true
  },
  images: {
    unoptimized: true
  },
  experimental: {
    webpackBuildWorker: true,
    parallelServerBuildTraces: true,
    parallelServerCompiles: true
  }
};
```

### 10.3 Vercel Configuration
```json
// vercel.json
{
  "version": 2,
  "installCommand": "pnpm install --no-frozen-lockfile",
  "buildCommand": "pnpm run build:vercel",
  "env": {
    "HUSKY": "0",
    "NODE_ENV": "production"
  }
}
```

### 10.4 Package Manager
- **Primary**: pnpm (Performance optimized)
- **Lock file**: pnpm-lock.yaml
- **Scripts**: Comprehensive npm scripts

### 10.5 Development Workflow
```bash
# Development
pnpm dev              # Start dev server on port 3001
pnpm lint             # Run ESLint
pnpm format           # Format code with Prettier
pnpm build            # Build for production

# Database
pnpm migrate-to-atlas # Migrate to MongoDB Atlas
pnpm seed-rich-data   # Seed database with sample data

# Testing
pnpm test:product-apis        # Test product APIs
pnpm test:product-inventory   # Test inventory flow
```

### 10.6 Code Quality Tools
```json
// ESLint + Prettier + Husky
"lint-staged": {
  "**/*.{js,jsx,tsx,ts,css,less,scss,sass}": [
    "prettier --write --no-error-on-unmatched-pattern"
  ]
}
```

---

## 11. Tính năng Chính

### 11.1 Customer Features
#### 11.1.1 Shopping Experience
- **Product Browsing**: Category-based navigation
- **Search & Filter**: Advanced product search
- **Product Details**: Image gallery, specifications, reviews
- **Shopping Cart**: Add/remove items, quantity management
- **Checkout**: Multi-step checkout process
- **Order Tracking**: Real-time order status updates

#### 11.1.2 Account Management
- **User Registration/Login**: Email-based authentication
- **Profile Management**: Personal information, preferences
- **Address Book**: Multiple shipping addresses
- **Order History**: Complete purchase history
- **Notifications**: Order updates, promotions

### 11.2 Seller Features
#### 11.2.1 Shop Management
- **Shop Setup**: Create and customize shop profile
- **Product Management**: Add, edit, delete products
- **Inventory Control**: Stock tracking, low-stock alerts
- **Order Processing**: Order fulfillment workflow
- **Customer Communication**: Order notes, updates

#### 11.2.2 Analytics & Reports
- **Sales Dashboard**: Revenue charts, KPI metrics
- **Product Analytics**: Best sellers, performance metrics
- **Inventory Reports**: Stock levels, movement history
- **Financial Reports**: Revenue, profit analysis
- **Export Functionality**: PDF/Excel report generation

### 11.3 Advanced Features
#### 11.3.1 Inventory Management
- **Stock Tracking**: Real-time inventory updates
- **Batch Operations**: Bulk inventory updates
- **History Logging**: Complete audit trail
- **Threshold Alerts**: Automatic low-stock notifications
- **Barcode Support**: QR code generation and scanning

#### 11.3.2 Analytics System
- **Real-time Data**: Live sales and inventory data
- **Custom Reports**: Flexible reporting system
- **Data Visualization**: Charts and graphs
- **Export Options**: Multiple format support
- **Historical Analysis**: Trend analysis and forecasting

### 11.4 Technical Features
#### 11.4.1 Performance
- **Server-Side Rendering**: Fast initial page loads
- **Image Optimization**: Next.js Image component
- **Caching Strategy**: React Query caching
- **Code Splitting**: Automatic bundle optimization
- **Lazy Loading**: Component-level lazy loading

#### 11.4.2 User Experience
- **Responsive Design**: Mobile-first approach
- **Dark/Light Theme**: User preference support
- **Accessibility**: ARIA labels, keyboard navigation
- **Loading States**: Skeleton screens, spinners
- **Error Handling**: User-friendly error messages

---

## 12. Scripts và Utilities

### 12.1 Database Scripts
```bash
# Migration và Seeding
scripts/migrate-to-atlas.js      # Migrate to MongoDB Atlas
scripts/seed-rich-data.js        # Seed comprehensive sample data
scripts/seed-specific-collections.js  # Seed specific collections
```

### 12.2 Testing Scripts
```bash
# API Testing
scripts/test-product-apis.js           # Test product CRUD operations
scripts/test-product-inventory-flow.js # Test inventory management
```

### 12.3 Development Utilities
```bash
# Code Quality
scripts/prepare.js               # Husky setup script

# Package Management
pnpm clean                      # Clean node_modules and lock file
pnpm reinstall                  # Clean install dependencies
```

### 12.4 Build Scripts
```json
{
  "dev": "next dev -p 3001",
  "build": "next build",
  "build:vercel": "HUSKY=0 next build",
  "start": "next start",
  "lint": "next lint",
  "lint:fix": "eslint src --fix && pnpm format",
  "format": "prettier --write ."
}
```

---

## 13. Testing và Quality Assurance

### 13.1 Testing Strategy
- **API Testing**: Manual testing với scripts
- **Component Testing**: React component testing
- **Integration Testing**: End-to-end user flows
- **Performance Testing**: Load testing và optimization

### 13.2 Code Quality Tools
```typescript
// ESLint Configuration
- TypeScript ESLint rules
- Next.js specific rules
- React hooks rules
- Accessibility rules

// Prettier Configuration
- Consistent code formatting
- Tailwind CSS class sorting
- Import sorting

// Husky Git Hooks
- Pre-commit: Lint staged files
- Pre-push: Run tests
```

### 13.3 Testing Files Structure
```
logs/                           # Test execution logs
├── product-inventory-test-*.log
└── ...

docs/testing-guide.md          # Testing documentation
```

### 13.4 Quality Metrics
- **TypeScript**: Strict type checking
- **ESLint**: Zero warnings policy
- **Prettier**: Consistent formatting
- **Build**: Zero build errors
- **Performance**: Core Web Vitals optimization

---

## 14. Hướng dẫn Migration

### 14.1 Yêu cầu Hệ thống
```bash
# Runtime Requirements
Node.js: >= 18.0.0
pnpm: >= 8.0.0
MongoDB: >= 6.0.0

# Development Requirements
TypeScript: 5.7.2
Next.js: 15.1.0
React: 18.3.1
```

### 14.2 Environment Setup
```bash
# 1. Clone repository
git clone <repository-url>
cd zen-buy-fe

# 2. Install dependencies
pnpm install

# 3. Setup environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# 4. Setup database
# Option A: Local MongoDB
mongod --dbpath /path/to/data

# Option B: MongoDB Atlas
# Update MONGODB_URI in .env.local

# 5. Seed database
pnpm seed-rich-data

# 6. Start development server
pnpm dev
```

### 14.3 Database Migration
```bash
# Migrate to MongoDB Atlas
pnpm migrate-to-atlas

# Seed with sample data
pnpm seed-rich-data

# Test API endpoints
pnpm test:product-apis
```

### 14.4 Deployment Migration
```bash
# Vercel Deployment
1. Connect GitHub repository to Vercel
2. Configure environment variables
3. Deploy automatically on push

# Environment Variables for Production
MONGODB_URI=<atlas-connection-string>
JWT_SECRET=<secure-random-string>
NODE_ENV=production
```

### 14.5 Data Migration Considerations
- **User Data**: Preserve user accounts and preferences
- **Product Data**: Maintain product catalog and inventory
- **Order History**: Keep complete order records
- **File Uploads**: Migrate uploaded images and documents
- **Analytics Data**: Preserve historical analytics data

---

## 15. Kết luận và Khuyến nghị

### 15.1 Điểm Mạnh của Hệ thống
1. **Kiến trúc Modern**: Next.js 15 với App Router
2. **Type Safety**: Full TypeScript implementation
3. **Performance**: SSR + optimized caching
4. **Scalability**: MongoDB + React Query
5. **Developer Experience**: Comprehensive tooling
6. **Internationalization**: Multi-language support
7. **Security**: JWT + role-based access control

### 15.2 Khuyến nghị Phát triển
1. **Testing**: Implement comprehensive test suite
2. **Monitoring**: Add application monitoring
3. **Performance**: Implement advanced caching strategies
4. **Security**: Add rate limiting and advanced security measures
5. **Documentation**: Maintain API documentation
6. **CI/CD**: Enhance deployment pipeline

### 15.3 Roadmap Tương lai
1. **Phase 1**: Complete core features implementation
2. **Phase 2**: Advanced analytics and reporting
3. **Phase 3**: Mobile app development
4. **Phase 4**: Third-party integrations
5. **Phase 5**: AI-powered features

### 15.4 Liên hệ và Hỗ trợ
- **Repository**: GitHub repository
- **Documentation**: `/docs` directory
- **Issues**: GitHub Issues
- **Author**: shadowhira

---

*Tài liệu này được tạo để hỗ trợ việc migration và phát triển tiếp dự án ZenBuy. Vui lòng cập nhật tài liệu khi có thay đổi trong hệ thống.*