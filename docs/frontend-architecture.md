# Frontend Architecture - E-commerce Platform

## 🎯 Tổng quan

Kiến trúc frontend hiện đại, scalable cho hệ thống thương mại điện tử, tương thích với backend NestJS + MongoDB.

## 🏗️ Kiến trúc Tổng thể

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
├─────────────────────────────────────────────────────────────┤
│  Customer App     │  Admin Dashboard  │  Seller Dashboard   │
│  (Next.js)        │  (Next.js)        │  (Next.js)          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    API Layer                                │
├─────────────────────────────────────────────────────────────┤
│           HTTP Client + React Query                         │
│           (Axios + TanStack Query)                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 Backend (Existing)                         │
├─────────────────────────────────────────────────────────────┤
│              NestJS + MongoDB                               │
└─────────────────────────────────────────────────────────────┘
```

## 📁 Cấu trúc Dự án

### Monorepo Structure
```
ecommerce-frontend/
├── apps/
│   ├── customer/               # Customer-facing app
│   ├── admin/                  # Admin dashboard
│   └── seller/                 # Seller dashboard
├── packages/
│   ├── ui/                     # Shared UI components
│   ├── api/                    # API client & types
│   ├── utils/                  # Shared utilities
│   ├── config/                 # Shared configurations
│   └── types/                  # Shared TypeScript types
├── tools/
│   ├── eslint-config/          # ESLint configurations
│   ├── tsconfig/               # TypeScript configurations
│   └── tailwind-config/        # Tailwind configurations
├── docs/                       # Documentation
├── turbo.json                  # Turborepo configuration
└── package.json                # Root package.json
```

### Individual App Structure
```
apps/customer/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # Auth pages group
│   │   ├── (shop)/            # Shopping pages group
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # App-specific components
│   │   ├── features/          # Feature-based components
│   │   ├── layouts/           # Layout components
│   │   └── pages/             # Page-specific components
│   ├── hooks/                 # Custom React hooks
│   ├── stores/                # State management
│   ├── lib/                   # App-specific utilities
│   └── types/                 # App-specific types
├── public/                    # Static assets
├── next.config.js             # Next.js configuration
├── tailwind.config.js         # Tailwind configuration
└── package.json               # App dependencies
```

## 🛠️ Tech Stack

### Core Technologies
```json
{
  "framework": "Next.js 14+ (App Router)",
  "language": "TypeScript 5+",
  "ui": "React 18",
  "styling": "Tailwind CSS",
  "components": "Shadcn/ui + Radix UI",
  "state": "Zustand + TanStack Query",
  "forms": "React Hook Form + Zod",
  "http": "Axios",
  "build": "Turbo (Turborepo)",
  "package_manager": "pnpm"
}
```

### UI & Styling Stack
```json
{
  "css_framework": "Tailwind CSS 3.4+",
  "component_library": "Shadcn/ui",
  "headless_ui": "Radix UI",
  "icons": "Lucide React",
  "animations": "Framer Motion",
  "charts": "Recharts",
  "date_picker": "React Day Picker",
  "rich_text": "Tiptap Editor"
}
```

### Development Tools
```json
{
  "linting": "ESLint 9+",
  "formatting": "Prettier",
  "type_checking": "TypeScript",
  "testing": "Vitest + Testing Library",
  "e2e_testing": "Playwright",
  "git_hooks": "Husky + lint-staged"
}
```

## 🎨 Component Architecture

### Atomic Design Pattern
```
packages/ui/src/
├── atoms/                     # Basic building blocks
│   ├── Button/
│   │   ├── Button.tsx
│   │   ├── Button.stories.tsx
│   │   └── index.ts
│   ├── Input/
│   ├── Badge/
│   ├── Avatar/
│   └── Spinner/
├── molecules/                 # Simple combinations
│   ├── SearchBox/
│   ├── ProductCard/
│   ├── UserMenu/
│   ├── PriceDisplay/
│   └── Pagination/
├── organisms/                 # Complex components
│   ├── ProductGrid/
│   ├── CheckoutForm/
│   ├── OrderSummary/
│   ├── Navigation/
│   └── DataTable/
├── templates/                 # Page layouts
│   ├── ShopLayout/
│   ├── CheckoutLayout/
│   ├── DashboardLayout/
│   └── AuthLayout/
└── providers/                 # Context providers
    ├── ThemeProvider/
    ├── QueryProvider/
    └── ToastProvider/
```

### Feature-based Components
```
apps/customer/src/components/features/
├── auth/
│   ├── LoginForm/
│   ├── RegisterForm/
│   ├── ForgotPasswordForm/
│   └── index.ts
├── products/
│   ├── ProductList/
│   ├── ProductDetail/
│   ├── ProductFilters/
│   ├── ProductSearch/
│   └── index.ts
├── cart/
│   ├── CartDrawer/
│   ├── CartItem/
│   ├── CartSummary/
│   └── index.ts
└── orders/
    ├── OrderHistory/
    ├── OrderDetail/
    ├── OrderTracking/
    └── index.ts
```

## 🔄 State Management Strategy

### 1. Global State với Zustand
```typescript
// packages/stores/src/auth.store.ts
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,

  login: async (credentials) => {
    set({ isLoading: true });
    try {
      const response = await authApi.login(credentials);
      set({
        user: response.user,
        isAuthenticated: true,
        isLoading: false
      });
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },

  logout: () => {
    authApi.logout();
    set({ user: null, isAuthenticated: false });
  },

  refreshToken: async () => {
    try {
      const response = await authApi.refreshToken();
      set({ user: response.user });
    } catch (error) {
      get().logout();
    }
  }
}));
```

### 2. Server State với TanStack Query
```typescript
// packages/api/src/hooks/products.hooks.ts
export function useProducts(filters: ProductFilters) {
  return useQuery({
    queryKey: ['products', filters],
    queryFn: () => productApi.getProducts(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000,   // 10 minutes
  });
}

export function useProduct(id: string) {
  return useQuery({
    queryKey: ['products', id],
    queryFn: () => productApi.getProduct(id),
    enabled: !!id,
  });
}

export function useCreateProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: productApi.createProduct,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
}
```

### 3. Form State với React Hook Form
```typescript
// apps/customer/src/components/features/auth/LoginForm.tsx
const loginSchema = z.object({
  email: z.string().email('Invalid email'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export function LoginForm() {
  const { login } = useAuthStore();

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      await login(data);
    } catch (error) {
      form.setError('root', { message: 'Invalid credentials' });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        {/* Form fields */}
      </form>
    </Form>
  );
}
```

## 🌐 API Integration Layer

### HTTP Client Configuration
```typescript
// packages/api/src/client.ts
import axios, { AxiosInstance } from 'axios';
import { useAuthStore } from '@/stores/auth.store';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor - Add auth token
    this.client.interceptors.request.use((config) => {
      const { user } = useAuthStore.getState();
      if (user?.token) {
        config.headers.Authorization = `Bearer ${user.token}`;
      }
      return config;
    });

    // Response interceptor - Handle errors
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          const { refreshToken, logout } = useAuthStore.getState();
          try {
            await refreshToken();
            return this.client.request(error.config);
          } catch {
            logout();
          }
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, params?: any): Promise<T> {
    const response = await this.client.get(url, { params });
    return response.data;
  }

  async post<T>(url: string, data?: any): Promise<T> {
    const response = await this.client.post(url, data);
    return response.data;
  }

  async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.client.put(url, data);
    return response.data;
  }

  async delete<T>(url: string): Promise<T> {
    const response = await this.client.delete(url);
    return response.data;
  }
}

export const apiClient = new ApiClient();
```

### API Services
```typescript
// packages/api/src/services/product.service.ts
export class ProductService {
  async getProducts(filters: ProductFilters): Promise<ProductsResponse> {
    return apiClient.get('/products', filters);
  }

  async getProduct(id: string): Promise<Product> {
    return apiClient.get(`/products/${id}`);
  }

  async createProduct(data: CreateProductData): Promise<Product> {
    return apiClient.post('/products', data);
  }

  async updateProduct(id: string, data: UpdateProductData): Promise<Product> {
    return apiClient.put(`/products/${id}`, data);
  }

  async deleteProduct(id: string): Promise<void> {
    return apiClient.delete(`/products/${id}`);
  }

  async searchProducts(query: string): Promise<ProductsResponse> {
    return apiClient.get('/products/search', { q: query });
  }
}

export const productService = new ProductService();
```

### Type Definitions
```typescript
// packages/types/src/api.types.ts
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface Product {
  _id: string;
  name: string;
  slug: string;
  description: string;
  price: number;
  comparePrice?: number;
  images: string[];
  category: Category;
  shop: Shop;
  inventory: number;
  status: 'active' | 'inactive' | 'draft';
  createdAt: string;
  updatedAt: string;
}

export interface User {
  _id: string;
  email: string;
  name: string;
  role: 'customer' | 'seller' | 'admin';
  avatar?: string;
  token?: string;
}
```

## 🎨 UI Component System

### Design System Configuration
```typescript
// packages/ui/src/theme/index.ts
export const theme = {
  colors: {
    primary: {
      50: '#eff6ff',
      500: '#3b82f6',
      900: '#1e3a8a',
    },
    gray: {
      50: '#f9fafb',
      500: '#6b7280',
      900: '#111827',
    },
  },
  spacing: {
    xs: '0.5rem',
    sm: '1rem',
    md: '1.5rem',
    lg: '2rem',
    xl: '3rem',
  },
  typography: {
    fontFamily: {
      sans: ['Inter', 'sans-serif'],
      mono: ['JetBrains Mono', 'monospace'],
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
    },
  },
} as const;
```

### Base Components
```typescript
// packages/ui/src/atoms/Button/Button.tsx
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'underline-offset-4 hover:underline text-primary',
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-9 px-3 rounded-md',
        lg: 'h-11 px-8 rounded-md',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
```

## 🔧 Development Workflow

### Package Scripts
```json
{
  "scripts": {
    "dev": "turbo run dev",
    "build": "turbo run build",
    "lint": "turbo run lint",
    "type-check": "turbo run type-check",
    "test": "turbo run test",
    "test:e2e": "turbo run test:e2e",
    "clean": "turbo run clean",
    "format": "prettier --write \"**/*.{ts,tsx,md}\"",
    "changeset": "changeset",
    "version-packages": "changeset version",
    "release": "turbo run build --filter=!@repo/docs && changeset publish"
  }
}
```

### Turbo Configuration
```json
// turbo.json
{
  "$schema": "https://turbo.build/schema.json",
  "globalDependencies": ["**/.env.*local"],
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": [".next/**", "!.next/cache/**"]
    },
    "lint": {
      "dependsOn": ["^lint"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "type-check": {
      "dependsOn": ["^type-check"]
    }
  }
}
```

## 🧪 Testing Strategy

### Unit Testing với Vitest
```typescript
// packages/ui/src/atoms/Button/Button.test.tsx
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from './Button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument();
  });

  it('handles click events', async () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    await userEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies variant styles correctly', () => {
    render(<Button variant="destructive">Delete</Button>);
    expect(screen.getByRole('button')).toHaveClass('bg-destructive');
  });
});
```

### E2E Testing với Playwright
```typescript
// apps/customer/tests/e2e/auth.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test('user can login successfully', async ({ page }) => {
    await page.goto('/login');

    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');

    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });

  test('shows error for invalid credentials', async ({ page }) => {
    await page.goto('/login');

    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');

    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
  });
});
```

## 🚀 Performance Optimization

### Code Splitting Strategy
```typescript
// apps/customer/src/app/products/[id]/page.tsx
import dynamic from 'next/dynamic';
import { Suspense } from 'react';

// Lazy load heavy components
const ProductReviews = dynamic(() => import('@/components/features/products/ProductReviews'), {
  loading: () => <ReviewsSkeleton />,
});

const RelatedProducts = dynamic(() => import('@/components/features/products/RelatedProducts'), {
  loading: () => <ProductGridSkeleton />,
});

export default function ProductPage({ params }: { params: { id: string } }) {
  return (
    <div>
      <ProductDetail id={params.id} />

      <Suspense fallback={<ReviewsSkeleton />}>
        <ProductReviews productId={params.id} />
      </Suspense>

      <Suspense fallback={<ProductGridSkeleton />}>
        <RelatedProducts productId={params.id} />
      </Suspense>
    </div>
  );
}
```

### Image Optimization
```typescript
// packages/ui/src/molecules/ProductCard/ProductCard.tsx
import Image from 'next/image';

export function ProductCard({ product }: { product: Product }) {
  return (
    <div className="group relative">
      <div className="aspect-square overflow-hidden rounded-lg">
        <Image
          src={product.images[0]}
          alt={product.name}
          width={300}
          height={300}
          className="object-cover transition-transform group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          priority={false}
        />
      </div>
      {/* Product info */}
    </div>
  );
}
```

### Caching Strategy
```typescript
// packages/api/src/hooks/products.hooks.ts
export function useProducts(filters: ProductFilters) {
  return useQuery({
    queryKey: ['products', filters],
    queryFn: () => productService.getProducts(filters),
    staleTime: 5 * 60 * 1000,     // 5 minutes
    gcTime: 10 * 60 * 1000,       // 10 minutes
    refetchOnWindowFocus: false,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}
```

## 🔒 Security Best Practices

### Input Validation
```typescript
// packages/utils/src/validation.ts
import { z } from 'zod';

export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export const productSchema = z.object({
  name: z.string().min(1, 'Product name is required').max(255),
  description: z.string().max(5000, 'Description too long'),
  price: z.number().positive('Price must be positive'),
  images: z.array(z.string().url()).max(10, 'Maximum 10 images allowed'),
});
```

### XSS Protection
```typescript
// packages/utils/src/sanitize.ts
import DOMPurify from 'dompurify';

export function sanitizeHtml(html: string): string {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li'],
    ALLOWED_ATTR: [],
  });
}

export function escapeHtml(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}
```

## 🌍 Internationalization

### i18n Setup
```typescript
// packages/config/src/i18n.config.ts
export const i18nConfig = {
  locales: ['en', 'vi', 'ja', 'zh'],
  defaultLocale: 'vi',
  localeDetection: false,
} as const;

export type Locale = typeof i18nConfig.locales[number];
```

### Translation Hook
```typescript
// packages/utils/src/hooks/use-translation.ts
import { useRouter } from 'next/router';
import { translations } from '@/locales';

export function useTranslation(namespace: string) {
  const { locale = 'vi' } = useRouter();

  const t = (key: string, params?: Record<string, string>) => {
    const translation = translations[locale]?.[namespace]?.[key] || key;

    if (params) {
      return Object.entries(params).reduce(
        (str, [key, value]) => str.replace(`{{${key}}}`, value),
        translation
      );
    }

    return translation;
  };

  return { t, locale };
}
```

## 📱 Responsive Design

### Breakpoint System
```typescript
// packages/ui/src/theme/breakpoints.ts
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

export const mediaQueries = {
  sm: `@media (min-width: ${breakpoints.sm})`,
  md: `@media (min-width: ${breakpoints.md})`,
  lg: `@media (min-width: ${breakpoints.lg})`,
  xl: `@media (min-width: ${breakpoints.xl})`,
  '2xl': `@media (min-width: ${breakpoints['2xl']})`,
} as const;
```

### Mobile-First Components
```typescript
// packages/ui/src/organisms/Navigation/Navigation.tsx
export function Navigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <nav className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Desktop Navigation */}
          <div className="hidden md:flex md:items-center md:space-x-8">
            <NavigationLinks />
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2 rounded-md text-gray-400 hover:text-gray-500"
            >
              <MenuIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <MobileNavigationLinks />
          </div>
        )}
      </div>
    </nav>
  );
}
```

## 🎯 Deployment Strategy

### Environment Configuration
```typescript
// packages/config/src/env.ts
import { z } from 'zod';

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']),
  NEXT_PUBLIC_API_URL: z.string().url(),
  NEXT_PUBLIC_APP_URL: z.string().url(),
  NEXT_PUBLIC_SENTRY_DSN: z.string().optional(),
});

export const env = envSchema.parse(process.env);
```

### Build Optimization
```javascript
// apps/customer/next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    optimizePackageImports: ['@repo/ui', 'lucide-react'],
  },
  images: {
    domains: ['your-api-domain.com'],
    formats: ['image/webp', 'image/avif'],
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  bundleBundleAnalyzer: {
    enabled: process.env.ANALYZE === 'true',
  },
};

module.exports = nextConfig;
```

## 📊 Monitoring & Analytics

### Error Tracking
```typescript
// packages/utils/src/monitoring.ts
import * as Sentry from '@sentry/nextjs';

export function initMonitoring() {
  Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
    environment: process.env.NODE_ENV,
    tracesSampleRate: 1.0,
  });
}

export function captureError(error: Error, context?: Record<string, any>) {
  Sentry.captureException(error, { extra: context });
}
```

### Performance Monitoring
```typescript
// packages/utils/src/performance.ts
export function measurePerformance<T>(
  name: string,
  fn: () => Promise<T>
): Promise<T> {
  return new Promise((resolve, reject) => {
    const start = performance.now();

    fn()
      .then((result) => {
        const end = performance.now();
        console.log(`${name} took ${end - start} milliseconds`);
        resolve(result);
      })
      .catch(reject);
  });
}
```

## 🎯 Kết luận

### Ưu điểm của Kiến trúc này:

✅ **Scalable**: Monorepo cho phép chia sẻ code hiệu quả
✅ **Type-safe**: Full TypeScript từ API đến UI
✅ **Performance**: Optimized với Next.js 14+ và React Query
✅ **Developer Experience**: Excellent DX với Turbo + modern tools
✅ **Maintainable**: Clear separation of concerns
✅ **Flexible**: Dễ dàng thêm apps mới (mobile, admin)
✅ **Modern**: Latest best practices và tools

### Roadmap Triển khai:

1. **Phase 1**: Setup monorepo + core packages
2. **Phase 2**: Customer app với basic features
3. **Phase 3**: Seller dashboard
4. **Phase 4**: Admin dashboard
5. **Phase 5**: Mobile app (React Native)

### Tương thích với Backend:

- ✅ NestJS APIs qua HTTP/REST
- ✅ MongoDB với `_id` fields
- ✅ JWT authentication
- ✅ File upload handling
- ✅ Real-time updates (WebSocket)

Kiến trúc này sẽ cho phép bạn xây dựng một hệ thống e-commerce hiện đại, scalable và maintainable trong tương lai!