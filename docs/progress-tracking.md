# Bảng theo dõi tiến độ phát triển dự án ZenBuy

## Hướng dẫn sử dụng
- <PERSON><PERSON>h dấu hoàn thành: ✅
- <PERSON><PERSON> thực hiện: 🔄
- Ch<PERSON><PERSON> bắt đầu: ⬜
- Gặp vấn đề: ⚠️

## Sprint 1: <PERSON><PERSON><PERSON> thực và Điều hướng (Tuần 1-2)

### Tuần 1: <PERSON><PERSON><PERSON> thiện xác thực và UI đăng nhập/đăng ký

#### Backend (API)
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 1.1.1 | Tạo API refresh token | ✅ | | Đã tạo API refresh token để làm mới token xác thực |
| 1.1.2 | C<PERSON>i thiện API kiểm tra trạng thái đăng nhập | ✅ | | <PERSON><PERSON> cải thiện API kiểm tra trạng thái đăng nhập với thông tin token hết hạn |
| 1.1.3 | Thêm middleware xác thực cho các routes | ✅ | | Đã cải thiện middleware với xác thực chi tiết và phân quyền |
| 1.1.4 | Tạo API lấy thông tin chi tiết người dùng | ✅ | | Đã cải thiện API profile với thông tin chi tiết và thống kê |
| 1.1.5 | Tạo API cập nhật thông tin người dùng | ✅ | | Đã cải thiện API cập nhật thông tin người dùng và thêm API đổi mật khẩu |

#### Frontend
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 1.1.6 | Thiết kế lại giao diện đăng nhập | ✅ | | Đã cải thiện giao diện đăng nhập với thiết kế mới |
| 1.1.7 | Thiết kế lại giao diện đăng ký | ✅ | | Đã cải thiện giao diện đăng ký với thiết kế mới và thêm tùy chọn vai trò |
| 1.1.8 | Thêm xác thực form với thông báo lỗi | ✅ | | Đã thêm xác thực form với Zod và React Hook Form |
| 1.1.9 | Thêm tùy chọn "Nhớ mật khẩu" | ✅ | | Đã thêm tùy chọn "Nhớ mật khẩu" với thời gian hết hạn cookie tùy chỉnh |
| 1.1.10 | Đảm bảo responsive trên các thiết bị | ✅ | | Đã đảm bảo giao diện responsive trên desktop và mobile |
| 1.1.11 | Thêm middleware kiểm tra xác thực trên client | ✅ | | Đã cải thiện middleware với chức năng redirect và lưu URL callback |

### Tuần 2: Điều hướng và trang hồ sơ người dùng

#### Backend (API)
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 1.2.1 | Tạo API quản lý địa chỉ giao hàng (CRUD) | ✅ | | Đã tạo API quản lý địa chỉ giao hàng với đầy đủ chức năng CRUD |
| 1.2.2 | Tạo API thay đổi mật khẩu | ✅ | | Đã tạo API thay đổi mật khẩu ở phần trước (1.1.5) |
| 1.2.3 | Tạo API lấy lịch sử đơn hàng của người dùng | ✅ | | Đã tạo API lấy lịch sử đơn hàng với phân trang và lọc theo trạng thái |
| 1.2.4 | Kiểm thử API xác thực và hồ sơ người dùng | ✅ | | Đã kiểm thử các API xác thực và hồ sơ người dùng |

#### Frontend
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 1.2.5 | Thêm cơ chế chuyển hướng đến trang đăng nhập | ✅ | | Đã thêm hook useAuthRedirect để xử lý chuyển hướng đến trang đăng nhập |
| 1.2.6 | Thêm cơ chế ghi nhớ URL trước khi chuyển hướng | ✅ | | Đã thêm component AuthGuard và cơ chế lưu URL callback |
| 1.2.7 | Cải thiện breadcrumb navigation | ✅ | | Đã tạo component BreadcrumbNav với tính năng tự động tạo breadcrumb |
| 1.2.8 | Tạo trang hồ sơ người dùng cơ bản | ✅ | | Đã tạo trang hồ sơ người dùng với tabs và Suspense |
| 1.2.9 | Thêm form cập nhật thông tin cá nhân | ✅ | | Đã thêm form cập nhật thông tin cá nhân với validation |
| 1.2.10 | Thêm quản lý địa chỉ giao hàng | ✅ | | Đã thêm quản lý địa chỉ giao hàng với CRUD đầy đủ |
| 1.2.11 | Thêm chức năng thay đổi mật khẩu | ✅ | | Đã thêm form thay đổi mật khẩu với validation |
| 1.2.12 | Kiểm thử luồng xác thực và hồ sơ người dùng | ✅ | | Đã kiểm thử luồng xác thực và hồ sơ người dùng |

## Sprint 2: Quản lý đơn hàng (Tuần 3-4)

### Tuần 3: API đơn hàng và thanh toán

#### Backend (API)
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 2.1.1 | Tạo API lấy danh sách đơn hàng (người mua) | ✅ | | Đã cải thiện API lấy danh sách đơn hàng với bộ lọc và sắp xếp |
| 2.1.2 | Tạo API lấy danh sách đơn hàng (người bán) | ✅ | | Đã tạo API lấy danh sách đơn hàng cho người bán |
| 2.1.3 | Tạo API lấy chi tiết đơn hàng | ✅ | | Đã cải thiện API lấy chi tiết đơn hàng với thông tin đầy đủ |
| 2.1.4 | Tạo API cập nhật trạng thái đơn hàng | ✅ | | Đã tạo API cập nhật trạng thái đơn hàng với lịch sử trạng thái |
| 2.1.5 | Tạo API hủy đơn hàng | ✅ | | Đã tạo API hủy đơn hàng và hoàn trả số lượng sản phẩm |
| 2.1.6 | Tạo API xử lý thanh toán nội bộ | ✅ | | Đã tạo API xử lý thanh toán nội bộ với nhiều phương thức thanh toán |
| 2.1.7 | Tạo API kiểm tra trạng thái thanh toán | ✅ | | Đã tạo API kiểm tra trạng thái thanh toán |
| 2.1.8 | Tạo API xác nhận thanh toán | ✅ | | Đã tạo API xác nhận thanh toán cho admin và seller |

#### Frontend
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 2.1.9 | Tạo trang danh sách đơn hàng (người bán) | ✅ | | Đã tạo trang danh sách đơn hàng cho người bán với bộ lọc và phân trang |
| 2.1.10 | Thêm bộ lọc đơn hàng theo trạng thái | ✅ | | Đã thêm bộ lọc đơn hàng theo trạng thái cho cả người mua và người bán |
| 2.1.11 | Cải thiện trang danh sách đơn hàng (người mua) | ✅ | | Đã cải thiện trang danh sách đơn hàng với tabs và hiển thị thông tin chi tiết hơn |
| 2.1.12 | Cải thiện form địa chỉ giao hàng trong trang thanh toán | ✅ | | Đã tạo components riêng cho form địa chỉ giao hàng, phương thức thanh toán và tóm tắt đơn hàng |

### Tuần 4: Hoàn thiện quản lý đơn hàng và thanh toán

#### Backend (API)
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 2.2.1 | Tạo API gửi email xác nhận đơn hàng | ✅ | | Đã tạo API gửi email xác nhận đơn hàng |
| 2.2.2 | Tạo API gửi email cập nhật trạng thái đơn hàng | ✅ | | Đã tích hợp gửi email khi cập nhật trạng thái đơn hàng |
| 2.2.3 | Kiểm thử API đơn hàng và thanh toán | ✅ | | Đã kiểm thử các API đơn hàng và thanh toán |

#### Frontend
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 2.2.4 | Tạo trang chi tiết đơn hàng (người bán) | ✅ | | Đã tạo trang chi tiết đơn hàng cho người bán |
| 2.2.5 | Thêm chức năng cập nhật trạng thái đơn hàng | ✅ | | Đã thêm chức năng cập nhật trạng thái đơn hàng |
| 2.2.6 | Thêm chức năng in hóa đơn | ✅ | | Đã tạo component PrintInvoice để in hóa đơn |
| 2.2.7 | Tạo trang chi tiết đơn hàng (người mua) | ✅ | | Đã cải thiện trang chi tiết đơn hàng cho người mua |
| 2.2.8 | Thêm timeline trạng thái đơn hàng | ✅ | | Đã tạo component OrderTimeline để hiển thị lịch sử trạng thái |
| 2.2.9 | Thêm chức năng hủy đơn hàng | ✅ | | Đã thêm chức năng hủy đơn hàng cho người mua |
| 2.2.10 | Thêm tùy chọn lưu địa chỉ giao hàng | ✅ | | Đã thêm tùy chọn lưu địa chỉ giao hàng trong form thanh toán |
| 2.2.11 | Cải thiện hiển thị tóm tắt đơn hàng | ✅ | | Đã tạo component OrderSummary để hiển thị tóm tắt đơn hàng |
| 2.2.12 | Thêm xử lý khi thanh toán thất bại | ✅ | | Đã tạo trang xử lý khi thanh toán thất bại và thành công |
| 2.2.13 | Kiểm thử luồng đơn hàng và thanh toán | ✅ | | Đã kiểm thử luồng đơn hàng và thanh toán |

## Sprint 3: Quản lý sản phẩm và kho hàng (Tuần 5-6)

### Tuần 5: API sản phẩm và kho hàng nâng cao

#### Backend (API)
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 3.1.1 | Cải thiện API thêm/sửa sản phẩm | ✅ | | Đã cải thiện API với validation, thêm trường specifications và minStockThreshold |
| 3.1.2 | Tạo API nhân bản sản phẩm | ✅ | | Đã tạo API nhân bản sản phẩm với slug tự động |
| 3.1.3 | Tạo API quản lý hình ảnh sản phẩm | ✅ | | Đã tạo API thêm, xóa và sắp xếp lại hình ảnh sản phẩm |
| 3.1.4 | Tạo API lấy lịch sử nhập/xuất kho | ✅ | | Đã tạo model InventoryHistory và API lấy lịch sử |
| 3.1.5 | Tạo API cập nhật tồn kho hàng loạt | ✅ | | Đã tạo API cập nhật tồn kho hàng loạt với ghi lịch sử |
| 3.1.6 | Tạo API cài đặt ngưỡng tồn kho tối thiểu | ✅ | | Đã tạo API cài đặt và lấy ngưỡng tồn kho tối thiểu |
| 3.1.7 | Tạo API lấy đánh giá của sản phẩm | ✅ | | Đã tạo API lấy đánh giá với phân trang và sắp xếp |
| 3.1.8 | Tạo API thêm đánh giá mới | ✅ | | Đã tạo API thêm đánh giá mới với validation |

#### Frontend
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 3.1.9 | Cải thiện form thêm/sửa sản phẩm (bước 1: thông tin cơ bản) | ✅ | | Đã tạo component BasicInfoForm với các trường mới |
| 3.1.10 | Cải thiện form thêm/sửa sản phẩm (bước 2: hình ảnh) | ✅ | | Đã tạo component ImagesManager với drag-and-drop và sắp xếp |
| 3.1.11 | Cải thiện form thêm/sửa sản phẩm (bước 3: thông số kỹ thuật) | ✅ | | Đã tạo component SpecificationsForm với templates |
| 3.1.12 | Thêm trình soạn thảo văn bản phong phú cho mô tả sản phẩm | ✅ | | Đã tạo component RichTextEditor với nhiều tính năng định dạng |

### Tuần 6: Hoàn thiện quản lý sản phẩm và kho hàng

#### Backend (API)
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 3.2.1 | Tạo API cập nhật/xóa đánh giá | ✅ | | Đã tạo API cập nhật và xóa đánh giá với validation |
| 3.2.2 | Kiểm thử API sản phẩm và kho hàng | ✅ | | Đã tạo script test-product-apis.js để kiểm thử API |
| 3.2.3 | Kiểm thử API đánh giá sản phẩm | ✅ | | Đã thêm kiểm thử API đánh giá vào script test-product-apis.js |
| 3.2.16 | Tạo API xóa sản phẩm | ✅ | | Đã cập nhật API xóa sản phẩm để xóa cả dữ liệu liên quan |

#### Frontend
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 3.2.4 | Thêm chức năng nhân bản sản phẩm | ✅ | | Đã tạo component ProductActions với chức năng nhân bản sản phẩm |
| 3.2.5 | Cải thiện quản lý hình ảnh sản phẩm | ✅ | | Đã tạo component ImagesManager với drag-and-drop và sắp xếp |
| 3.2.6 | Tạo trang quản lý kho hàng nâng cao | ✅ | | Đã tạo trang quản lý kho hàng với nhiều tab chức năng |
| 3.2.7 | Thêm lịch sử nhập/xuất kho | ✅ | | Đã tạo component InventoryHistory hiển thị lịch sử kho hàng |
| 3.2.8 | Thêm cảnh báo khi hàng sắp hết | ✅ | | Đã tạo component LowStockAlerts hiển thị cảnh báo khi hàng sắp hết |
| 3.2.9 | Thêm chức năng nhập/xuất kho hàng loạt | ✅ | | Đã tạo component BatchUpdate cho phép cập nhật tồn kho hàng loạt |
| 3.2.10 | Thêm cài đặt ngưỡng tồn kho tối thiểu | ✅ | | Đã tạo component ThresholdDialog để cài đặt ngưỡng tồn kho tối thiểu |
| 3.2.11 | Cải thiện trang chi tiết sản phẩm (người mua) | ✅ | | Đã cải thiện trang chi tiết sản phẩm với layout mới |
| 3.2.12 | Thêm gallery hình ảnh với chức năng zoom cơ bản | ✅ | | Đã tạo component ImageGallery với chức năng zoom và lightbox |
| 3.2.13 | Thêm tab "Thông số kỹ thuật" | ✅ | | Đã tạo component ProductInfoTabs với tab mô tả và thông số kỹ thuật |
| 3.2.14 | Cải thiện hiển thị đánh giá sản phẩm | ✅ | | Đã cải thiện component ProductReviews với thống kê đánh giá |
| 3.2.15 | Kiểm thử luồng quản lý sản phẩm và kho hàng | ✅ | | Đã tạo script test-product-inventory-flow.js để kiểm thử toàn bộ luồng |

## Sprint 4: Phân tích và Báo cáo (Tuần 7-8)

### Tuần 7: API phân tích và thông báo

#### Backend (API)
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 4.1.1 | Cải thiện API lấy dữ liệu phân tích | ✅ | | Đã cải thiện API với thêm tham số lọc và so sánh |
| 4.1.2 | Tạo API tạo báo cáo tùy chỉnh | ✅ | | Đã tạo API tạo báo cáo doanh thu, sản phẩm và tồn kho |
| 4.1.3 | Tạo API xuất báo cáo | ✅ | | Đã tạo API xuất báo cáo ra CSV và JSON |
| 4.1.4 | Tạo API lấy danh sách thông báo | ✅ | | Đã tạo API lấy danh sách thông báo với phân trang và lọc |
| 4.1.5 | Tạo API đánh dấu thông báo đã đọc | ✅ | | Đã tạo API đánh dấu thông báo đã đọc và đánh dấu tất cả |
| 4.1.6 | Tạo API cài đặt thông báo | ✅ | | Đã tạo API cài đặt thông báo với nhiều loại thông báo |

#### Frontend
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 4.1.7 | Thêm widget tổng quan đơn hàng vào dashboard | ✅ | | Đã tạo widget hiển thị tổng quan đơn hàng theo trạng thái |
| 4.1.8 | Cải thiện biểu đồ doanh thu với tùy chọn khoảng thời gian | ✅ | | Đã cải thiện biểu đồ với nhiều tùy chọn thời gian và so sánh |
| 4.1.9 | Thêm các chỉ số KPI quan trọng vào dashboard | ✅ | | Đã thêm các chỉ số KPI như tỷ lệ chuyển đổi, giá trị đơn hàng trung bình |
| 4.1.10 | Tạo component thông báo | ✅ | | Đã tạo component dropdown thông báo và tích hợp vào header |
| 4.1.11 | Tích hợp thông báo với sự kiện đơn hàng mới | ✅ | | Đã tích hợp thông báo với sự kiện đơn hàng mới |
| 4.1.12 | Tích hợp thông báo với sự kiện hàng sắp hết | ✅ | | Đã tạo widget cảnh báo hàng sắp hết và tích hợp với thông báo |

### Tuần 8: Hoàn thiện phân tích, báo cáo và kiểm thử tổng thể

#### Backend (API)
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 4.2.1 | Kiểm thử API phân tích và báo cáo | ✅ | | Đã kiểm thử API phân tích và báo cáo với các tham số khác nhau |
| 4.2.2 | Kiểm thử API thông báo | ✅ | | Đã kiểm thử API thông báo với các tham số khác nhau |
| 4.2.3 | Kiểm thử tích hợp toàn bộ hệ thống | ✅ | | Đã kiểm thử tích hợp toàn bộ hệ thống |

#### Frontend
| ID | Công việc | Trạng thái | Người thực hiện | Ghi chú |
|----|-----------|------------|-----------------|---------|
| 4.2.4 | Thêm trang quản lý thông báo | ✅ | | Đã tạo trang quản lý thông báo với các tùy chọn lọc và phân trang |
| 4.2.5 | Thêm cài đặt thông báo | ✅ | | Đã tạo component cài đặt thông báo với các tùy chọn khác nhau |
| 4.2.6 | Cải thiện trang phân tích với các biểu đồ nâng cao | ✅ | | Đã cải thiện trang phân tích với nhiều biểu đồ và tùy chọn |
| 4.2.7 | Thêm trang báo cáo doanh thu | ✅ | | Đã tạo trang báo cáo doanh thu với biểu đồ và bảng dữ liệu |
| 4.2.8 | Thêm trang báo cáo sản phẩm | ✅ | | Đã tạo trang báo cáo sản phẩm với biểu đồ và bảng dữ liệu |
| 4.2.9 | Thêm chức năng xuất báo cáo | ✅ | | Đã thêm chức năng xuất báo cáo ra CSV và JSON |
| 4.2.10 | Kiểm thử luồng phân tích và báo cáo | ✅ | | Đã kiểm thử luồng phân tích và báo cáo |
| 4.2.11 | Kiểm thử tổng thể hệ thống | ✅ | | Đã kiểm thử tổng thể hệ thống |
| 4.2.12 | Tối ưu hóa hiệu suất | ✅ | | Đã tối ưu hóa hiệu suất của các trang phân tích và báo cáo |

## Tổng kết tiến độ

| Sprint | Tổng số công việc | Hoàn thành | Tỷ lệ hoàn thành |
|--------|-------------------|------------|------------------|
| Sprint 1 | 23 | 23 | 100% |
| Sprint 2 | 21 | 21 | 100% |
| Sprint 3 | 27 | 29 | 100% | ✅ Hoàn thành |
| Sprint 4 | 23 | 23 | 100% | ✅ Hoàn thành |
| Tổng cộng | 94 | 96 | 100% | ✅ Hoàn thành |
