import { NextRequest } from 'next/server';
import { cookies } from 'next/headers';
import { verifyToken } from './jwt';
import User from '@/models/User';
import dbConnect from './mongodb';
import type { IUser } from '@/models/User';
import mongoose from 'mongoose';

// Hàm tạm thời để bỏ qua xác thực và trả về một user mặc định
export async function getAuthUser(request: NextRequest | Request): Promise<IUser | null> {
  console.log('getAuthUser - TEMPORARY VERSION - Bypassing authentication');
  
  try {
    // Kết nối database
    await dbConnect();
    
    // Tìm một user bất kỳ để sử dụng làm user mặc định
    let defaultUser = await User.findOne({ role: 'seller' }).select('-password');
    
    // Nếu không tìm thấy user nào có role là seller, tìm user bất kỳ
    if (!defaultUser) {
      defaultUser = await User.findOne({}).select('-password');
    }
    
    // Nếu vẫn không tìm thấy user nào, tạo một user mặc định
    if (!defaultUser) {
      console.log('getAuthUser - Creating temporary default user');
      
      // Tạo một user mặc định
      defaultUser = new User({
        name: 'Default Seller',
        email: '<EMAIL>',
        password: 'password123',
        role: 'seller',
      });
      
      await defaultUser.save();
    }
    
    console.log('getAuthUser - Using default user:', defaultUser._id);
    return defaultUser;
  } catch (error) {
    console.error('Error in temporary getAuthUser:', error);
    
    // Nếu có lỗi, tạo một user giả không lưu vào database
    const fakeUserId = new mongoose.Types.ObjectId();
    
    // Tạo một đối tượng user giả
    const fakeUser = {
      _id: fakeUserId,
      name: 'Fake Seller',
      email: '<EMAIL>',
      role: 'seller',
      createdAt: new Date(),
      updatedAt: new Date(),
    } as IUser;
    
    console.log('getAuthUser - Using fake user:', fakeUserId);
    return fakeUser;
  }
}
