"use client"

import { useEffect, useState } from "react"
import { useRouter, usePathname, useSearchParams } from "next/navigation"
import { useAuthState } from "@store/auth/auth.state"
import { toast } from "sonner"
import { useTranslation } from "react-i18next"

interface AuthGuardProps {
  children: React.ReactNode
  requiredRoles?: string[]
  fallback?: React.ReactNode
  redirectTo?: string
}

/**
 * Component bảo vệ các trang yêu cầu xác thực
 * 
 * @param props Props của component
 * @returns Component đã được bảo vệ
 */
export function AuthGuard({
  children,
  requiredRoles = [],
  fallback,
  redirectTo = "/login",
}: AuthGuardProps) {
  const { t } = useTranslation("common")
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const { isAuthenticated, user } = useAuthState()
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Kiểm tra xác thực
    if (!isAuthenticated) {
      // Hiển thị thông báo
      toast.error(t("auth.pleaseLogin"))
      
      // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
      const currentUrl = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : "")
      const callbackUrl = encodeURIComponent(currentUrl)
      
      // Chuyển hướng đến trang đăng nhập
      router.push(`${redirectTo}?callbackUrl=${callbackUrl}`)
      return
    }

    // Kiểm tra quyền truy cập
    if (requiredRoles.length > 0) {
      const hasRequiredRole = user && requiredRoles.includes(user.role)
      setIsAuthorized(hasRequiredRole)
      
      if (!hasRequiredRole) {
        toast.error(t("auth.noPermission"))
        router.push("/")
        return
      }
    } else {
      setIsAuthorized(true)
    }

    setIsLoading(false)
  }, [isAuthenticated, user, requiredRoles, router, pathname, searchParams, redirectTo, t])

  // Hiển thị fallback khi đang tải hoặc không có quyền truy cập
  if (isLoading || !isAuthorized) {
    return fallback || null
  }

  // Hiển thị nội dung khi đã xác thực và có quyền truy cập
  return <>{children}</>
}
