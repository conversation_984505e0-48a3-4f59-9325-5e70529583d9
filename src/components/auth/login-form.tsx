"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { useLogin } from "@hooks/use-auth"
import { Button } from "@components/ui/button"
import { Input } from "@components/ui/input"
import { Label } from "@components/ui/label"
import { useRouter, useSearchParams } from "next/navigation"
import { Checkbox } from "@components/ui/checkbox"
import { useTranslation } from "react-i18next"
import { Eye, EyeOff, Mail, Lock, AlertCircle } from "lucide-react"
import { toast } from "sonner"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"

const formSchema = z.object({
  email: z.string().email({
    message: "<PERSON><PERSON> không hợp lệ.",
  }),
  password: z.string().min(6, {
    message: "<PERSON><PERSON>t khẩu phải có ít nhất 6 ký tự.",
  }),
  rememberMe: z.boolean().default(false),
})

export default function LoginForm() {
  const { t } = useTranslation("auth")
  const login = useLogin()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [showPassword, setShowPassword] = useState(false)

  // Lấy callbackUrl từ query params nếu có
  let callbackUrl = searchParams.get("callbackUrl") || "/"
  const tokenExpired = searchParams.get("tokenExpired") === "true"

  // Nếu callbackUrl là URL được mã hóa, giải mã nó
  try {
    if (callbackUrl.includes('%')) {
      callbackUrl = decodeURIComponent(callbackUrl)
    }
  } catch (e) {
    console.error("Error decoding callbackUrl:", e)
  }

  console.log("Callback URL (decoded):", callbackUrl)

  // Hiển thị thông báo nếu token đã hết hạn
  useEffect(() => {
    if (tokenExpired) {
      toast.error(t("login.tokenExpired"))
    }
  }, [tokenExpired, t])

  // Khởi tạo form với react-hook-form và zod validation
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  })

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await login.mutateAsync({
        email: values.email,
        password: values.password,
        rememberMe: values.rememberMe,
      })

      // Hiển thị thông báo thành công
      toast.success(t("login.success"))

      // Redirect sau khi đăng nhập thành công
      console.log("Redirecting to:", callbackUrl)

      // Sử dụng window.location.href trực tiếp để chuyển hướng
      setTimeout(() => {
        // Kiểm tra xem callbackUrl có phải là URL đầy đủ không
        if (callbackUrl.startsWith('http')) {
          window.location.href = callbackUrl
        } else {
          // Đảm bảo callbackUrl bắt đầu bằng /
          const formattedUrl = callbackUrl.startsWith('/') ? callbackUrl : `/${callbackUrl}`

          // Lấy locale hiện tại
          const locale = window.location.pathname.split('/')[1]

          // Tạo URL đầy đủ với locale
          const fullUrl = `/${locale}${formattedUrl}`

          console.log("Final redirect URL:", fullUrl)
          window.location.href = fullUrl
        }
      }, 1000)
    } catch (error) {
      console.log('error: ', error);
      // Hiển thị thông báo lỗi
      toast.error(error instanceof Error ? error.message : "Đăng nhập thất bại")
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl">{t("login.welcomeBack")}</CardTitle>
        <CardDescription>
          {t("login.enterCredentials")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("login.emailLabel")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder={t("login.emailPlaceholder")}
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("login.passwordLabel")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        type={showPassword ? "text" : "password"}
                        placeholder={t("login.passwordPlaceholder")}
                        className="pl-10"
                        {...field}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                        <span className="sr-only">
                          {showPassword ? t("login.hidePassword") : t("login.showPassword")}
                        </span>
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex items-center justify-between">
              <FormField
                control={form.control}
                name="rememberMe"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="text-sm font-normal">
                      {t("login.rememberMe")}
                    </FormLabel>
                  </FormItem>
                )}
              />

              <Button variant="link" className="px-0 text-sm font-normal" asChild>
                <a href="/forgot-password">{t("login.forgotPassword")}</a>
              </Button>
            </div>

            {login.error && (
              <div className="flex items-center gap-2 rounded-md bg-destructive/15 p-3 text-sm text-destructive">
                <AlertCircle className="h-4 w-4" />
                <p>{login.error instanceof Error ? login.error.message : String(login.error)}</p>
              </div>
            )}

            <Button type="submit" className="w-full" disabled={login.isPending}>
              {login.isPending ? t("login.loggingIn") : t("login.loginButton")}
            </Button>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex flex-col items-center justify-center space-y-2">
        <div className="text-sm text-muted-foreground">
          {t("login.noAccount")}{" "}
          <Button variant="link" className="p-0 text-sm" asChild>
            <a href="/register">{t("login.register")}</a>
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
