"use client"

import { useForm } from "react-hook-form"
import { useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useTranslation } from "react-i18next"

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form"
import { RadioGroup, RadioGroupItem } from "@components/ui/radio-group"
import { PaymentMethod } from "@/types"

const formSchema = z.object({
  paymentMethod: z.enum(["credit_card", "bank_transfer", "cash"], {
    required_error: "Bạn cần chọn phương thức thanh toán.",
  }),
});

type FormValues = z.infer<typeof formSchema>

interface PaymentMethodFormProps {
  onSubmit: (data: { paymentMethod: PaymentMethod }) => void;
  defaultValue?: PaymentMethod;
}

export default function PaymentMethodForm({
  onSubmit,
  defaultValue = "credit_card"
}: PaymentMethodFormProps) {
  const { t } = useTranslation("checkout");

  // Form definition
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      paymentMethod: defaultValue,
    },
  });

  // Form submission handler
  const handleSubmit = (data: FormValues) => {
    onSubmit({ paymentMethod: data.paymentMethod });
  };

  // Submit form when component mounts with default values
  useEffect(() => {
    // Submit default value
    handleSubmit(form.getValues());
  }, []);

  return (
    <div className="bg-card rounded-lg p-6 shadow-sm">
      <h2 className="text-xl font-semibold mb-4">{t("paymentMethod") || "Phương thức thanh toán"}</h2>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="paymentMethod"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-2"
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="credit_card" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer">
                        {t("creditCard") || "Thẻ tín dụng/ghi nợ"}
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="bank_transfer" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer">
                        {t("bankTransfer") || "Chuyển khoản ngân hàng"}
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="cash" />
                      </FormControl>
                      <FormLabel className="font-normal cursor-pointer">
                        {t("cashOnDelivery") || "Thanh toán khi nhận hàng"}
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </div>
  );
}
