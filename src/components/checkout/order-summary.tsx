"use client"

import { useTranslation } from "react-i18next"
import { Separator } from "@components/ui/separator"
import { CartItem } from "@/types"

interface OrderSummaryProps {
  items: CartItem[];
  subtotal: number;
  shipping: number;
  total: number;
}

export default function OrderSummary({ 
  items, 
  subtotal, 
  shipping, 
  total 
}: OrderSummaryProps) {
  const { t } = useTranslation("checkout");

  return (
    <div className="bg-card rounded-lg p-6 shadow-sm sticky top-4">
      <h2 className="text-xl font-semibold mb-4">{t("orderSummary") || "Tóm tắt đơn hàng"}</h2>
      
      <div className="space-y-4">
        {items?.map((item) => (
          <div key={item._id || item.id} className="flex space-x-4">
            <div className="w-16 h-16 bg-muted rounded relative overflow-hidden">
              {item.image && (
                <img 
                  src={item.image} 
                  alt={item.name || "Sản phẩm"} 
                  className="object-cover w-full h-full"
                />
              )}
            </div>
            <div className="flex-1">
              <p className="font-medium">{item.name}</p>
              <p className="text-muted-foreground text-sm">
                {t("quantity") || "Số lượng"}: {item.quantity}
              </p>
              <p className="font-medium">
                {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' })
                  .format(item.price * item.quantity)}
              </p>
            </div>
          </div>
        ))}
      </div>
      
      <Separator className="my-4" />
      
      <div className="space-y-2">
        <div className="flex justify-between">
          <span>{t("subtotal") || "Tạm tính"}</span>
          <span>
            {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' })
              .format(subtotal)}
          </span>
        </div>
        <div className="flex justify-between">
          <span>{t("shipping") || "Phí vận chuyển"}</span>
          <span>
            {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' })
              .format(shipping)}
          </span>
        </div>
        <Separator className="my-2" />
        <div className="flex justify-between font-semibold">
          <span>{t("total") || "Tổng cộng"}</span>
          <span>
            {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' })
              .format(total)}
          </span>
        </div>
      </div>
    </div>
  );
}
