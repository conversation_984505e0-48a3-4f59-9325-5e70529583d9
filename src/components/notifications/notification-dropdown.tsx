"use client"

import { useState, useEffect } from "react"
import { Bell } from "lucide-react"
import { Button } from "@components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@components/ui/dropdown-menu"
import { Badge } from "@components/ui/badge"
import { useNotifications, useNotificationListener } from "@hooks/use-notifications"
import { useNotificationsState } from "@store/notifications/notifications.state"
import { formatDistanceToNow } from "date-fns"
import { vi } from "date-fns/locale"
import { useRouter } from "next/navigation"
import { useTranslation } from "react-i18next"

export function NotificationDropdown() {
  const { t } = useTranslation("common")
  const router = useRouter()
  const [open, setOpen] = useState(false)
  const notificationsState = useNotificationsState()
  const { data, isLoading, mark<PERSON>R<PERSON>, markAllAsRead } = useNotifications(1, 5, "false")
  const { hasNewNotification, clearNewNotificationFlag } = useNotificationListener()
  
  // Hiệu ứng nhấp nháy khi có thông báo mới
  const [blink, setBlink] = useState(false)
  
  useEffect(() => {
    if (hasNewNotification) {
      setBlink(true)
      const timeout = setTimeout(() => setBlink(false), 1000)
      return () => clearTimeout(timeout)
    }
  }, [hasNewNotification])

  // Xử lý khi mở dropdown
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen)
    if (isOpen) {
      clearNewNotificationFlag()
    }
  }

  // Xử lý khi click vào thông báo
  const handleNotificationClick = (id: string, type: string, data?: Record<string, any>) => {
    markAsRead(id)
    setOpen(false)
    
    // Chuyển hướng dựa vào loại thông báo
    switch (type) {
      case "order":
        if (data?.orderId) {
          router.push(`/seller/orders/${data.orderId}`)
        } else {
          router.push("/seller/orders")
        }
        break
      case "inventory":
        router.push("/seller/inventory")
        break
      case "payment":
        if (data?.orderId) {
          router.push(`/seller/orders/${data.orderId}`)
        } else {
          router.push("/seller/orders")
        }
        break
      default:
        router.push("/seller/notifications")
    }
  }

  // Xử lý khi click vào "Xem tất cả"
  const handleViewAll = () => {
    setOpen(false)
    router.push("/seller/notifications")
  }

  // Xử lý khi click vào "Đánh dấu tất cả đã đọc"
  const handleMarkAllAsRead = () => {
    markAllAsRead()
  }

  // Lấy icon cho từng loại thông báo
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "order":
        return "🛒"
      case "inventory":
        return "📦"
      case "payment":
        return "💰"
      default:
        return "🔔"
    }
  }

  return (
    <DropdownMenu open={open} onOpenChange={handleOpenChange}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className={`h-5 w-5 ${blink ? "animate-pulse text-primary" : ""}`} />
          {notificationsState.unreadCount.get() > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {notificationsState.unreadCount.get() > 9 ? "9+" : notificationsState.unreadCount.get()}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80" align="end">
        <DropdownMenuLabel className="flex justify-between items-center">
          <span>{t("notifications")}</span>
          <Button variant="ghost" size="sm" onClick={handleMarkAllAsRead} disabled={notificationsState.unreadCount.get() === 0}>
            {t("markAllAsRead")}
          </Button>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup className="max-h-80 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center text-muted-foreground">{t("loading")}</div>
          ) : notificationsState.notifications.get().length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">{t("noNotifications")}</div>
          ) : (
            notificationsState.notifications.get().map((notification) => (
              <DropdownMenuItem
                key={notification._id}
                className="p-3 cursor-pointer"
                onClick={() => handleNotificationClick(notification._id, notification.type, notification.data)}
              >
                <div className="flex gap-3">
                  <div className="text-xl">{getNotificationIcon(notification.type)}</div>
                  <div className="flex-1">
                    <div className="font-medium">{notification.title}</div>
                    <div className="text-sm text-muted-foreground line-clamp-2">{notification.message}</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true, locale: vi })}
                    </div>
                  </div>
                </div>
              </DropdownMenuItem>
            ))
          )}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem className="p-2 cursor-pointer" onClick={handleViewAll}>
          <div className="w-full text-center font-medium">{t("viewAllNotifications")}</div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
