"use client"

import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useProfile, useUpdateProfile } from "@hooks/use-auth"
import { Button } from "@components/ui/button"
import { Switch } from "@components/ui/switch"
import { Label } from "@components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@components/ui/card"
import { toast } from "sonner"
import { Settings, Bell, Mail, Globe, Moon } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { useTheme } from "next-themes"
import { useRouter } from "next/navigation"

export function UserSettings() {
  const { t, i18n } = useTranslation("profile")
  const { data: user } = useProfile()
  const updateProfile = useUpdateProfile()
  const { theme, setTheme } = useTheme()
  const router = useRouter()
  
  const [preferences, setPreferences] = useState({
    newsletter: user?.preferences?.newsletter ?? true,
    notifications: user?.preferences?.notifications ?? true,
    language: user?.preferences?.language ?? i18n.language,
  })
  
  const handlePreferenceChange = async (key: keyof typeof preferences, value: boolean | string) => {
    try {
      const newPreferences = {
        ...preferences,
        [key]: value,
      }
      
      setPreferences(newPreferences)
      
      await updateProfile.mutateAsync({
        preferences: newPreferences,
      })
      
      toast.success(t("settings.preferencesUpdated"))
      
      // Nếu thay đổi ngôn ngữ, cập nhật ngôn ngữ hiện tại
      if (key === "language" && typeof value === "string") {
        // Lấy đường dẫn hiện tại
        const currentPath = window.location.pathname
        
        // Tìm locale hiện tại trong đường dẫn
        const pathParts = currentPath.split('/')
        if (pathParts.length > 1) {
          // Thay thế locale trong đường dẫn
          pathParts[1] = value
          const newPath = pathParts.join('/')
          
          // Chuyển hướng đến đường dẫn mới
          router.push(newPath)
        }
      }
    } catch (error) {
      toast.error(t("settings.updateError"))
      
      // Khôi phục giá trị cũ
      setPreferences({
        newsletter: user?.preferences?.newsletter ?? true,
        notifications: user?.preferences?.notifications ?? true,
        language: user?.preferences?.language ?? i18n.language,
      })
    }
  }
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center">
          <Settings className="mr-2 h-5 w-5" />
          <div>
            <CardTitle>{t("settings.title")}</CardTitle>
            <CardDescription>{t("settings.description")}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">{t("settings.notifications")}</h3>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <Label htmlFor="newsletter">{t("settings.newsletter")}</Label>
            </div>
            <Switch
              id="newsletter"
              checked={preferences.newsletter}
              onCheckedChange={(checked) => handlePreferenceChange("newsletter", checked)}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Bell className="h-4 w-4 text-muted-foreground" />
              <Label htmlFor="notifications">{t("settings.enableNotifications")}</Label>
            </div>
            <Switch
              id="notifications"
              checked={preferences.notifications}
              onCheckedChange={(checked) => handlePreferenceChange("notifications", checked)}
            />
          </div>
        </div>
        
        <div className="space-y-4">
          <h3 className="text-lg font-medium">{t("settings.appearance")}</h3>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Globe className="h-4 w-4 text-muted-foreground" />
              <Label htmlFor="language">{t("settings.language")}</Label>
            </div>
            <Select
              value={preferences.language}
              onValueChange={(value) => handlePreferenceChange("language", value)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t("settings.selectLanguage")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="vi">Tiếng Việt</SelectItem>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="ja">日本語</SelectItem>
                <SelectItem value="zh">中文</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Moon className="h-4 w-4 text-muted-foreground" />
              <Label htmlFor="theme">{t("settings.theme")}</Label>
            </div>
            <Select
              value={theme || "system"}
              onValueChange={(value) => setTheme(value)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t("settings.selectTheme")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="light">{t("settings.light")}</SelectItem>
                <SelectItem value="dark">{t("settings.dark")}</SelectItem>
                <SelectItem value="system">{t("settings.system")}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t px-6 py-4">
        <div className="text-sm text-muted-foreground">
          {t("settings.lastUpdated", { date: new Date(user?.updatedAt || Date.now()).toLocaleDateString() })}
        </div>
      </CardFooter>
    </Card>
  )
}
