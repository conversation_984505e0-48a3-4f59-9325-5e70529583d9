'use client'

import { useState } from 'react'
import { Button } from '@components/ui/button'
import { Input } from '@components/ui/input'
import { Label } from '@components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@components/ui/card'
import { Download, Loader2 } from 'lucide-react'

export default function SimpleQRGenerator() {
  const [text, setText] = useState('')
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  const generateQRCode = async () => {
    if (!text) return
    
    setLoading(true)
    try {
      // Dynamic import to avoid build issues
      const QRCode = await import('qrcode')
      
      // Generate QR code as data URL
      const url = await QRCode.toDataURL(text, {
        width: 512,
        margin: 1,
        errorCorrectionLevel: 'H'
      })
      
      setQrCodeUrl(url)
    } catch (error) {
      console.error('Error generating QR code:', error)
    } finally {
      setLoading(false)
    }
  }

  const downloadQRCode = () => {
    if (!qrCodeUrl) return
    
    const link = document.createElement('a')
    link.href = qrCodeUrl
    link.download = `qrcode-${Date.now()}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Simple QR Code Generator</CardTitle>
        <CardDescription>
          Enter text or URL to generate a QR code
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="text">Text or URL</Label>
          <Input
            id="text"
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="Enter text or URL"
          />
        </div>
        
        <Button 
          onClick={generateQRCode} 
          disabled={loading || !text}
          className="w-full"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            'Generate QR Code'
          )}
        </Button>
        
        {qrCodeUrl && (
          <div className="mt-6 flex flex-col items-center">
            <div className="border p-4 rounded-lg bg-white">
              <img src={qrCodeUrl} alt="QR Code" className="w-64 h-64" />
            </div>
            <Button 
              variant="outline" 
              onClick={downloadQRCode} 
              className="mt-4 gap-2"
            >
              <Download className="h-4 w-4" />
              Download QR Code
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
