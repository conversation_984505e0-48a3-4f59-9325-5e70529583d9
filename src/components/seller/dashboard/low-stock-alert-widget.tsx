"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@components/ui/card"
import { useTranslation } from "react-i18next"
import { useRouter } from "next/navigation"
import { AlertTriangle } from "lucide-react"
import { Button } from "@components/ui/button"
import { useQuery } from "@tanstack/react-query"
import { inventoryService } from "@services/inventory.service"
import { Skeleton } from "@components/ui/skeleton"

interface LowStockProduct {
  _id: string
  title: string
  stock: number
  minStockThreshold: number
  sku: string
  image?: string
}

export default function LowStockAlertWidget() {
  const { t } = useTranslation("seller-dashboard")
  const router = useRouter()
  
  // L<PERSON><PERSON> danh sách sản phẩm có tồn kho thấp
  const { data, isLoading, error } = useQuery({
    queryKey: ["lowStockProducts"],
    queryFn: () => inventoryService.getLowStockProducts(5), // <PERSON><PERSON>y tối đa 5 sản phẩm
    staleTime: 1000 * 60 * 5, // 5 phút
  })
  
  // Xử lý khi click vào sản phẩm
  const handleProductClick = (productId: string) => {
    router.push(`/seller/products/edit/${productId}`)
  }
  
  // Xử lý khi click vào nút "Xem tất cả"
  const handleViewAll = () => {
    router.push("/seller/inventory?tab=low-stock")
  }
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
            <Skeleton className="h-6 w-40" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex items-center justify-between py-2 border-b">
              <div className="flex items-center">
                <Skeleton className="h-10 w-10 rounded" />
                <div className="ml-3">
                  <Skeleton className="h-4 w-32 mb-1" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </div>
              <Skeleton className="h-6 w-16" />
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }
  
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
            {t("lowStockAlert")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">{t("errorLoadingData")}</p>
        </CardContent>
      </Card>
    )
  }
  
  const lowStockProducts = data?.products || []
  
  if (lowStockProducts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
            {t("lowStockAlert")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">{t("noLowStockProducts")}</p>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
          {t("lowStockAlert")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {lowStockProducts.map((product: LowStockProduct) => (
            <div 
              key={product._id} 
              className="flex items-center justify-between py-2 border-b cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800"
              onClick={() => handleProductClick(product._id)}
            >
              <div className="flex items-center">
                <div className="h-10 w-10 rounded bg-gray-200 overflow-hidden">
                  {product.image ? (
                    <img src={product.image} alt={product.title} className="h-full w-full object-cover" />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center text-gray-400">
                      <AlertTriangle className="h-5 w-5" />
                    </div>
                  )}
                </div>
                <div className="ml-3">
                  <p className="font-medium text-sm">{product.title}</p>
                  <p className="text-xs text-muted-foreground">SKU: {product.sku}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium text-red-500">{product.stock} / {product.minStockThreshold}</p>
                <p className="text-xs text-muted-foreground">{t("inStock")}</p>
              </div>
            </div>
          ))}
          
          <Button variant="outline" size="sm" className="w-full" onClick={handleViewAll}>
            {t("viewAllLowStock")}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
