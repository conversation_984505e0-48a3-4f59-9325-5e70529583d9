"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@components/ui/card"
import { useAnalyticsState } from "@store/seller/analytics/analytics.state"
import { useTranslation } from "react-i18next"
import { Package, Clock, Truck, CheckCircle, XCircle } from "lucide-react"
import { Progress } from "@components/ui/progress"
import { useRouter } from "next/navigation"

export default function OrderOverview() {
  const { t } = useTranslation("seller-dashboard")
  const analyticsState = useAnalyticsState()
  const router = useRouter()
  
  // Lấy dữ liệu từ state
  const orderStatuses = analyticsState.orderStatuses.get()
  const totalOrders = analyticsState.totalOrders.get()
  
  // Tính tỷ lệ phần trăm cho mỗi trạng thái
  const pendingPercent = totalOrders > 0 ? (orderStatuses.pending / totalOrders) * 100 : 0
  const processingPercent = totalOrders > 0 ? (orderStatuses.processing / totalOrders) * 100 : 0
  const shippedPercent = totalOrders > 0 ? (orderStatuses.shipped / totalOrders) * 100 : 0
  const deliveredPercent = totalOrders > 0 ? (orderStatuses.delivered / totalOrders) * 100 : 0
  const cancelledPercent = totalOrders > 0 ? (orderStatuses.cancelled / totalOrders) * 100 : 0
  
  // Xử lý khi click vào card
  const handleCardClick = (status: string) => {
    router.push(`/seller/orders?status=${status}`)
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("orderOverview")}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card 
            className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            onClick={() => handleCardClick("pending")}
          >
            <CardContent className="p-4 flex flex-col items-center">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-amber-100 text-amber-600 mb-2">
                <Clock className="h-6 w-6" />
              </div>
              <CardTitle className="text-lg mb-1">{orderStatuses.pending}</CardTitle>
              <p className="text-sm text-muted-foreground">{t("pendingOrders")}</p>
              <Progress value={pendingPercent} className="h-1 mt-2" />
            </CardContent>
          </Card>
          
          <Card 
            className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            onClick={() => handleCardClick("processing")}
          >
            <CardContent className="p-4 flex flex-col items-center">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 text-blue-600 mb-2">
                <Package className="h-6 w-6" />
              </div>
              <CardTitle className="text-lg mb-1">{orderStatuses.processing}</CardTitle>
              <p className="text-sm text-muted-foreground">{t("processingOrders")}</p>
              <Progress value={processingPercent} className="h-1 mt-2" />
            </CardContent>
          </Card>
          
          <Card 
            className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            onClick={() => handleCardClick("shipped")}
          >
            <CardContent className="p-4 flex flex-col items-center">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-indigo-100 text-indigo-600 mb-2">
                <Truck className="h-6 w-6" />
              </div>
              <CardTitle className="text-lg mb-1">{orderStatuses.shipped}</CardTitle>
              <p className="text-sm text-muted-foreground">{t("shippedOrders")}</p>
              <Progress value={shippedPercent} className="h-1 mt-2" />
            </CardContent>
          </Card>
          
          <Card 
            className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            onClick={() => handleCardClick("delivered")}
          >
            <CardContent className="p-4 flex flex-col items-center">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-green-100 text-green-600 mb-2">
                <CheckCircle className="h-6 w-6" />
              </div>
              <CardTitle className="text-lg mb-1">{orderStatuses.delivered}</CardTitle>
              <p className="text-sm text-muted-foreground">{t("deliveredOrders")}</p>
              <Progress value={deliveredPercent} className="h-1 mt-2" />
            </CardContent>
          </Card>
          
          <Card 
            className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            onClick={() => handleCardClick("cancelled")}
          >
            <CardContent className="p-4 flex flex-col items-center">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-red-100 text-red-600 mb-2">
                <XCircle className="h-6 w-6" />
              </div>
              <CardTitle className="text-lg mb-1">{orderStatuses.cancelled}</CardTitle>
              <p className="text-sm text-muted-foreground">{t("cancelledOrders")}</p>
              <Progress value={cancelledPercent} className="h-1 mt-2" />
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  )
}
