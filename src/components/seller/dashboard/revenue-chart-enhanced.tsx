"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@components/ui/card"
import { useAnalyticsState } from "@store/seller/analytics/analytics.state"
import { useTranslation } from "react-i18next"
import { Line } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { Tabs, TabsList, TabsTrigger } from "@components/ui/tabs"
import { Skeleton } from "@components/ui/skeleton"

// Đăng ký các thành phần Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

interface RevenueChartEnhancedProps {
  onTimeFrameChange?: (timeFrame: "day" | "3days" | "week" | "month" | "year") => void
  onComparisonChange?: (comparison: "previous" | "year_ago" | "none") => void
}

export default function RevenueChartEnhanced({
  onTimeFrameChange,
  onComparisonChange,
}: RevenueChartEnhancedProps) {
  const { t } = useTranslation("seller-dashboard")
  const analyticsState = useAnalyticsState()
  
  const [timeFrame, setTimeFrame] = useState<"day" | "3days" | "week" | "month" | "year">("week")
  const [comparison, setComparison] = useState<"previous" | "year_ago" | "none">("previous")
  const [chartType, setChartType] = useState<"revenue" | "orders">("revenue")
  
  // Lấy dữ liệu từ state
  const dailySales = analyticsState.dailySales.get()
  const metadata = analyticsState.metadata?.get()
  
  // Xử lý khi thay đổi timeFrame
  const handleTimeFrameChange = (value: string) => {
    const newTimeFrame = value as "day" | "3days" | "week" | "month" | "year"
    setTimeFrame(newTimeFrame)
    if (onTimeFrameChange) {
      onTimeFrameChange(newTimeFrame)
    }
  }
  
  // Xử lý khi thay đổi comparison
  const handleComparisonChange = (value: string) => {
    const newComparison = value as "previous" | "year_ago" | "none"
    setComparison(newComparison)
    if (onComparisonChange) {
      onComparisonChange(newComparison)
    }
  }
  
  // Chuẩn bị dữ liệu cho biểu đồ
  const chartData = {
    labels: dailySales.map(day => day.date),
    datasets: [
      {
        label: t("revenue"),
        data: dailySales.map(day => chartType === "revenue" ? day.revenue : day.orders),
        borderColor: "rgb(53, 162, 235)",
        backgroundColor: "rgba(53, 162, 235, 0.5)",
        tension: 0.3,
        fill: true,
        pointRadius: 3,
        pointHoverRadius: 5,
      },
    ],
  }
  
  // Tùy chọn cho biểu đồ
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            let label = context.dataset.label || ""
            if (label) {
              label += ": "
            }
            if (chartType === "revenue") {
              label += new Intl.NumberFormat("vi-VN", {
                style: "currency",
                currency: "VND",
                maximumFractionDigits: 0,
              }).format(context.parsed.y)
            } else {
              label += context.parsed.y
            }
            return label
          },
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value: any) {
            if (chartType === "revenue") {
              return new Intl.NumberFormat("vi-VN", {
                style: "currency",
                currency: "VND",
                notation: "compact",
                compactDisplay: "short",
                maximumFractionDigits: 0,
              }).format(value)
            }
            return value
          },
        },
      },
    },
  }
  
  return (
    <Card className="col-span-4">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>{t("revenueOverTime")}</CardTitle>
        <div className="flex items-center space-x-2">
          <Tabs defaultValue="revenue" onValueChange={(value) => setChartType(value as "revenue" | "orders")}>
            <TabsList>
              <TabsTrigger value="revenue">{t("revenue")}</TabsTrigger>
              <TabsTrigger value="orders">{t("orders")}</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <Select defaultValue={timeFrame} onValueChange={handleTimeFrameChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder={t("timeFrame")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">{t("day")}</SelectItem>
              <SelectItem value="3days">{t("3days")}</SelectItem>
              <SelectItem value="week">{t("week")}</SelectItem>
              <SelectItem value="month">{t("month")}</SelectItem>
              <SelectItem value="year">{t("year")}</SelectItem>
            </SelectContent>
          </Select>
          
          <Select defaultValue={comparison} onValueChange={handleComparisonChange}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder={t("comparison")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="previous">{t("previousPeriod")}</SelectItem>
              <SelectItem value="year_ago">{t("yearAgo")}</SelectItem>
              <SelectItem value="none">{t("noComparison")}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          {dailySales.length > 0 ? (
            <Line data={chartData} options={chartOptions} />
          ) : (
            <div className="flex items-center justify-center h-full">
              <Skeleton className="h-[250px] w-full" />
            </div>
          )}
        </div>
        
        {metadata && (
          <div className="mt-4 text-xs text-muted-foreground">
            <p>
              {t("period")}: {new Date(metadata.startDate).toLocaleDateString()} - {new Date(metadata.endDate).toLocaleDateString()}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
