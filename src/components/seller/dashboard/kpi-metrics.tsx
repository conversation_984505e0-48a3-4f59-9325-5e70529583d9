"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@components/ui/card"
import { useAnalyticsState } from "@store/seller/analytics/analytics.state"
import { useTranslation } from "react-i18next"
import { TrendingUp, Users, DollarSign, ShoppingBag, Repeat } from "lucide-react"
import { Skeleton } from "@components/ui/skeleton"

interface KpiCardProps {
  title: string
  value: string | number
  change?: number
  icon: React.ReactNode
  loading?: boolean
}

function KpiCard({ title, value, change, icon, loading = false }: KpiCardProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <Skeleton className="h-4 w-24" />
          </CardTitle>
          <Skeleton className="h-4 w-4 rounded" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-7 w-16 mb-1" />
          <Skeleton className="h-4 w-20" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change !== undefined && (
          <p className={`text-xs ${change >= 0 ? "text-green-500" : "text-red-500"}`}>
            {change >= 0 ? "+" : ""}{change.toFixed(1)}% so với kỳ trước
          </p>
        )}
      </CardContent>
    </Card>
  )
}

export default function KpiMetrics() {
  const { t } = useTranslation("seller-dashboard")
  const analyticsState = useAnalyticsState()
  
  // Lấy dữ liệu từ state
  const totalRevenue = analyticsState.totalRevenue.get()
  const revenueChange = analyticsState.revenueChange.get()
  const totalOrders = analyticsState.totalOrders.get()
  const ordersChange = analyticsState.ordersChange.get()
  const activeCustomers = analyticsState.activeCustomers.get()
  const averageOrderValue = analyticsState.averageOrderValue.get()
  const conversionRate = analyticsState.conversionRate.get()
  
  // Lấy dữ liệu KPI nâng cao nếu có
  const advancedKpis = analyticsState.advancedKpis?.get()
  const averageRevenuePerCustomer = advancedKpis?.averageRevenuePerCustomer || 0
  const repeatPurchaseRate = advancedKpis?.repeatPurchaseRate || 1
  
  // Format tiền tệ
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      maximumFractionDigits: 0
    }).format(value)
  }
  
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
      <KpiCard
        title={t("totalRevenue")}
        value={formatCurrency(totalRevenue)}
        change={revenueChange}
        icon={<DollarSign className="h-4 w-4 text-muted-foreground" />}
      />
      
      <KpiCard
        title={t("totalOrders")}
        value={totalOrders}
        change={ordersChange}
        icon={<ShoppingBag className="h-4 w-4 text-muted-foreground" />}
      />
      
      <KpiCard
        title={t("activeCustomers")}
        value={activeCustomers}
        icon={<Users className="h-4 w-4 text-muted-foreground" />}
      />
      
      <KpiCard
        title={t("averageOrderValue")}
        value={formatCurrency(averageOrderValue)}
        icon={<TrendingUp className="h-4 w-4 text-muted-foreground" />}
      />
      
      <KpiCard
        title={t("repeatPurchaseRate")}
        value={repeatPurchaseRate.toFixed(2)}
        icon={<Repeat className="h-4 w-4 text-muted-foreground" />}
      />
    </div>
  )
}
