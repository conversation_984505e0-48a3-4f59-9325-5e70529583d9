"use client"

import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@components/ui/card"
import { Input } from "@components/ui/input"
import { <PERSON><PERSON> } from "@components/ui/button"
import { Badge } from "@components/ui/badge"
import { Skeleton } from "@components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@components/ui/pagination"
import { Search, Calendar, ArrowUpDown } from "lucide-react"
import { format, parseISO } from "date-fns"
import Image from "next/image"
import { Popover, PopoverContent, PopoverTrigger } from "@components/ui/popover"
import { Calendar as CalendarComponent } from "@components/ui/calendar"

interface InventoryHistoryEntry {
  _id: string
  product: {
    _id: string
    title: string
    slug: string
    images: string[]
  }
  user: {
    _id: string
    name: string
    email: string
  }
  actionType: "add" | "remove" | "adjust" | "threshold_update"
  quantity: number
  previousQuantity: number
  newQuantity: number
  notes: string
  createdAt: string
}

export default function InventoryHistory() {
  const { t } = useTranslation("seller")
  
  const [history, setHistory] = useState<InventoryHistoryEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [searchQuery, setSearchQuery] = useState("")
  const [actionTypeFilter, setActionTypeFilter] = useState<string>("all")
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)
  const [sortBy, setSortBy] = useState<string>("newest")

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        setLoading(true)
        
        // Build query parameters
        const params = new URLSearchParams()
        params.append("page", page.toString())
        params.append("limit", "10")
        
        if (searchQuery) {
          params.append("query", searchQuery)
        }
        
        if (actionTypeFilter !== "all") {
          params.append("actionType", actionTypeFilter)
        }
        
        if (startDate) {
          params.append("startDate", startDate.toISOString())
        }
        
        if (endDate) {
          params.append("endDate", endDate.toISOString())
        }
        
        if (sortBy) {
          params.append("sort", sortBy)
        }
        
        const response = await fetch(`/api/seller/inventory/history?${params.toString()}`)
        
        if (!response.ok) {
          throw new Error("Failed to fetch inventory history")
        }
        
        const data = await response.json()
        setHistory(data.history)
        setTotalPages(data.totalPages || Math.ceil(data.total / 10))
      } catch (error) {
        console.error("Error fetching inventory history:", error)
        setError(error instanceof Error ? error.message : "An unknown error occurred")
      } finally {
        setLoading(false)
      }
    }
    
    fetchHistory()
  }, [page, searchQuery, actionTypeFilter, startDate, endDate, sortBy])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPage(1) // Reset to first page when searching
  }

  const handleActionTypeFilterChange = (value: string) => {
    setActionTypeFilter(value)
    setPage(1) // Reset to first page when changing filter
  }

  const handleSortChange = (value: string) => {
    setSortBy(value)
    setPage(1) // Reset to first page when changing sort
  }

  const handleDateReset = () => {
    setStartDate(undefined)
    setEndDate(undefined)
    setPage(1) // Reset to first page when clearing dates
  }

  const getActionTypeBadge = (actionType: string) => {
    switch (actionType) {
      case "add":
        return (
          <Badge variant="success" className="bg-green-500">
            {t("stockAdded") || "Stock Added"}
          </Badge>
        )
      case "remove":
        return (
          <Badge variant="destructive">
            {t("stockRemoved") || "Stock Removed"}
          </Badge>
        )
      case "adjust":
        return (
          <Badge variant="outline">
            {t("stockAdjusted") || "Stock Adjusted"}
          </Badge>
        )
      case "threshold_update":
        return (
          <Badge variant="secondary">
            {t("thresholdUpdated") || "Threshold Updated"}
          </Badge>
        )
      default:
        return (
          <Badge variant="outline">
            {actionType}
          </Badge>
        )
    }
  }

  const renderPagination = () => {
    if (totalPages <= 1) return null
    
    return (
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious 
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
            />
          </PaginationItem>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            // Show pages around current page
            let pageNum
            if (totalPages <= 5) {
              pageNum = i + 1
            } else if (page <= 3) {
              pageNum = i + 1
            } else if (page >= totalPages - 2) {
              pageNum = totalPages - 4 + i
            } else {
              pageNum = page - 2 + i
            }
            
            return (
              <PaginationItem key={pageNum}>
                <PaginationLink
                  isActive={page === pageNum}
                  onClick={() => setPage(pageNum)}
                >
                  {pageNum}
                </PaginationLink>
              </PaginationItem>
            )
          })}
          
          <PaginationItem>
            <PaginationNext 
              onClick={() => setPage(p => Math.min(totalPages, p + 1))}
              disabled={page === totalPages}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("error") || "Error"}</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => window.location.reload()}>
            {t("tryAgain") || "Try Again"}
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <div className="flex flex-col sm:flex-row gap-4 items-end mb-6">
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder={t("searchProducts") || "Search products..."}
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </form>
        
        <div className="w-full sm:w-[180px]">
          <Select value={actionTypeFilter} onValueChange={handleActionTypeFilterChange}>
            <SelectTrigger>
              <span>{t("actionType") || "Action Type"}</span>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("allActions") || "All Actions"}</SelectItem>
              <SelectItem value="add">{t("stockAdded") || "Stock Added"}</SelectItem>
              <SelectItem value="remove">{t("stockRemoved") || "Stock Removed"}</SelectItem>
              <SelectItem value="adjust">{t("stockAdjusted") || "Stock Adjusted"}</SelectItem>
              <SelectItem value="threshold_update">{t("thresholdUpdated") || "Threshold Updated"}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="w-full sm:w-[180px]">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full justify-start text-left font-normal">
                <Calendar className="mr-2 h-4 w-4" />
                {startDate && endDate ? (
                  <span>
                    {format(startDate, "PP")} - {format(endDate, "PP")}
                  </span>
                ) : (
                  <span>{t("dateRange") || "Date Range"}</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <div className="p-3 space-y-3">
                <div className="space-y-1">
                  <h4 className="text-sm font-medium">
                    {t("startDate") || "Start Date"}
                  </h4>
                  <CalendarComponent
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                  />
                </div>
                <div className="space-y-1">
                  <h4 className="text-sm font-medium">
                    {t("endDate") || "End Date"}
                  </h4>
                  <CalendarComponent
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    disabled={(date) => startDate ? date < startDate : false}
                    initialFocus
                  />
                </div>
                <div className="flex justify-end">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleDateReset}
                    disabled={!startDate && !endDate}
                  >
                    {t("reset") || "Reset"}
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
        
        <div className="w-full sm:w-[180px]">
          <Select value={sortBy} onValueChange={handleSortChange}>
            <SelectTrigger>
              <div className="flex items-center">
                <ArrowUpDown className="mr-2 h-3.5 w-3.5" />
                <span>{t("sortBy") || "Sort by"}</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">{t("newest") || "Newest"}</SelectItem>
              <SelectItem value="oldest">{t("oldest") || "Oldest"}</SelectItem>
              <SelectItem value="quantity_high">{t("quantityHighToLow") || "Quantity: High to Low"}</SelectItem>
              <SelectItem value="quantity_low">{t("quantityLowToHigh") || "Quantity: Low to High"}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>{t("inventoryHistory") || "Inventory History"}</CardTitle>
          <CardDescription>
            {t("inventoryHistoryDesc") || "Track all changes to your inventory over time."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-md" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : history.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">
                {t("noHistoryFound") || "No inventory history found."}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("product") || "Product"}</TableHead>
                    <TableHead>{t("action") || "Action"}</TableHead>
                    <TableHead className="text-center">{t("change") || "Change"}</TableHead>
                    <TableHead className="text-center">{t("newStock") || "New Stock"}</TableHead>
                    <TableHead>{t("date") || "Date"}</TableHead>
                    <TableHead>{t("notes") || "Notes"}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {history.map((entry) => (
                    <TableRow key={entry._id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 relative rounded overflow-hidden bg-gray-100">
                            {entry.product.images && entry.product.images.length > 0 ? (
                              <Image
                                src={entry.product.images[0]}
                                alt={entry.product.title}
                                fill
                                className="object-cover"
                                sizes="40px"
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full w-full text-gray-400 text-xs">
                                No image
                              </div>
                            )}
                          </div>
                          <div className="font-medium truncate max-w-[200px]">
                            {entry.product.title}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getActionTypeBadge(entry.actionType)}
                      </TableCell>
                      <TableCell className="text-center">
                        {entry.actionType === "threshold_update" ? (
                          "-"
                        ) : entry.actionType === "add" ? (
                          <span className="text-green-600">+{entry.quantity}</span>
                        ) : entry.actionType === "remove" ? (
                          <span className="text-red-600">-{entry.quantity}</span>
                        ) : (
                          <span>
                            {entry.previousQuantity} → {entry.newQuantity}
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-center">
                        {entry.newQuantity}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {format(parseISO(entry.createdAt), "PPP")}
                        </div>
                        <div className="text-xs text-gray-500">
                          {format(parseISO(entry.createdAt), "p")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-[200px] truncate text-sm">
                          {entry.notes || "-"}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
          
          <div className="mt-6">
            {renderPagination()}
          </div>
        </CardContent>
      </Card>
    </>
  )
}
