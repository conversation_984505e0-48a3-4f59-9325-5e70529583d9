"use client"

import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@components/ui/card"
import { Input } from "@components/ui/input"
import { Button } from "@components/ui/button"
import { Badge } from "@components/ui/badge"
import { Skeleton } from "@components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@components/ui/pagination"
import { Search, ArrowUpDown, Plus, Minus, AlertTriangle } from "lucide-react"
import Image from "next/image"
import { StockUpdateDialog } from "./stock-update-dialog"

interface Product {
  _id: string
  title: string
  price: number
  stock: number
  minStockThreshold?: number
  category: {
    _id: string
    name: string
  }
  images: string[]
  createdAt: string
  updatedAt: string
}

export default function InventoryOverview() {
  const { t } = useTranslation("seller")
  const router = useRouter()
  
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState<string>("stock_low")
  const [stockFilter, setStockFilter] = useState<string>("all")
  
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [updateType, setUpdateType] = useState<"add" | "remove" | "set">("add")

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true)
        
        // Build query parameters
        const params = new URLSearchParams()
        params.append("page", page.toString())
        params.append("limit", "10")
        
        if (searchQuery) {
          params.append("query", searchQuery)
        }
        
        if (sortBy) {
          params.append("sort", sortBy)
        }
        
        if (stockFilter !== "all") {
          params.append("stockFilter", stockFilter)
        }
        
        const response = await fetch(`/api/seller/products?${params.toString()}`)
        
        if (!response.ok) {
          throw new Error("Failed to fetch products")
        }
        
        const data = await response.json()
        setProducts(data.products)
        setTotalPages(data.totalPages || Math.ceil(data.total / 10))
      } catch (error) {
        console.error("Error fetching products:", error)
        setError(error instanceof Error ? error.message : "An unknown error occurred")
      } finally {
        setLoading(false)
      }
    }
    
    fetchProducts()
  }, [page, searchQuery, sortBy, stockFilter])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPage(1) // Reset to first page when searching
  }

  const handleSortChange = (value: string) => {
    setSortBy(value)
    setPage(1) // Reset to first page when changing sort
  }

  const handleStockFilterChange = (value: string) => {
    setStockFilter(value)
    setPage(1) // Reset to first page when changing filter
  }

  const handleUpdateStock = (product: Product, type: "add" | "remove" | "set") => {
    setSelectedProduct(product)
    setUpdateType(type)
    setIsUpdateDialogOpen(true)
  }

  const handleStockUpdated = () => {
    // Refresh the product list
    const fetchProducts = async () => {
      try {
        const params = new URLSearchParams()
        params.append("page", page.toString())
        params.append("limit", "10")
        
        if (searchQuery) {
          params.append("query", searchQuery)
        }
        
        if (sortBy) {
          params.append("sort", sortBy)
        }
        
        if (stockFilter !== "all") {
          params.append("stockFilter", stockFilter)
        }
        
        const response = await fetch(`/api/seller/products?${params.toString()}`)
        
        if (!response.ok) {
          throw new Error("Failed to fetch products")
        }
        
        const data = await response.json()
        setProducts(data.products)
      } catch (error) {
        console.error("Error refreshing products:", error)
      }
    }
    
    fetchProducts()
  }

  const renderPagination = () => {
    if (totalPages <= 1) return null
    
    return (
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious 
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
            />
          </PaginationItem>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            // Show pages around current page
            let pageNum
            if (totalPages <= 5) {
              pageNum = i + 1
            } else if (page <= 3) {
              pageNum = i + 1
            } else if (page >= totalPages - 2) {
              pageNum = totalPages - 4 + i
            } else {
              pageNum = page - 2 + i
            }
            
            return (
              <PaginationItem key={pageNum}>
                <PaginationLink
                  isActive={page === pageNum}
                  onClick={() => setPage(pageNum)}
                >
                  {pageNum}
                </PaginationLink>
              </PaginationItem>
            )
          })}
          
          <PaginationItem>
            <PaginationNext 
              onClick={() => setPage(p => Math.min(totalPages, p + 1))}
              disabled={page === totalPages}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("error") || "Error"}</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => window.location.reload()}>
            {t("tryAgain") || "Try Again"}
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <div className="flex flex-col sm:flex-row gap-4 items-end mb-6">
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder={t("searchProducts") || "Search products..."}
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </form>
        
        <div className="w-full sm:w-[180px]">
          <Select value={sortBy} onValueChange={handleSortChange}>
            <SelectTrigger>
              <div className="flex items-center">
                <ArrowUpDown className="mr-2 h-3.5 w-3.5" />
                <span>{t("sortBy") || "Sort by"}</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="stock_low">{t("stockLowToHigh") || "Stock: Low to High"}</SelectItem>
              <SelectItem value="stock_high">{t("stockHighToLow") || "Stock: High to Low"}</SelectItem>
              <SelectItem value="name_asc">{t("nameAZ") || "Name: A-Z"}</SelectItem>
              <SelectItem value="name_desc">{t("nameZA") || "Name: Z-A"}</SelectItem>
              <SelectItem value="updated_recent">{t("recentlyUpdated") || "Recently Updated"}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="w-full sm:w-[180px]">
          <Select value={stockFilter} onValueChange={handleStockFilterChange}>
            <SelectTrigger>
              <span>{t("stockStatus") || "Stock Status"}</span>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("allProducts") || "All Products"}</SelectItem>
              <SelectItem value="in_stock">{t("inStock") || "In Stock"}</SelectItem>
              <SelectItem value="out_of_stock">{t("outOfStock") || "Out of Stock"}</SelectItem>
              <SelectItem value="low_stock">{t("lowStock") || "Low Stock"}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>{t("inventoryOverview") || "Inventory Overview"}</CardTitle>
          <CardDescription>
            {t("inventoryOverviewDesc") || "Manage your product stock levels and inventory."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-md" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">
                {searchQuery 
                  ? (t("noProductsFound") || "No products found matching your search.") 
                  : (t("noProductsYet") || "You haven't added any products yet.")}
              </p>
              <Button 
                className="mt-4"
                onClick={() => router.push("/seller/products/add")}
              >
                <Plus className="mr-2 h-4 w-4" />
                {t("addProduct") || "Add Product"}
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("product") || "Product"}</TableHead>
                    <TableHead className="text-center">{t("stock") || "Stock"}</TableHead>
                    <TableHead className="text-center">{t("threshold") || "Threshold"}</TableHead>
                    <TableHead className="text-center">{t("status") || "Status"}</TableHead>
                    <TableHead className="text-right">{t("actions") || "Actions"}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {products.map((product) => (
                    <TableRow key={product._id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 relative rounded overflow-hidden bg-gray-100">
                            {product.images && product.images.length > 0 ? (
                              <Image
                                src={product.images[0]}
                                alt={product.title}
                                fill
                                className="object-cover"
                                sizes="40px"
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full w-full text-gray-400 text-xs">
                                No image
                              </div>
                            )}
                          </div>
                          <div className="font-medium truncate max-w-[200px]">
                            {product.title}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge variant={product.stock > 0 ? "outline" : "destructive"}>
                          {product.stock}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-center">
                        {product.minStockThreshold !== undefined ? (
                          <Badge variant="secondary">
                            {product.minStockThreshold}
                          </Badge>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell className="text-center">
                        {product.stock === 0 ? (
                          <Badge variant="destructive">
                            {t("outOfStock") || "Out of Stock"}
                          </Badge>
                        ) : product.minStockThreshold !== undefined && product.stock <= product.minStockThreshold ? (
                          <Badge variant="warning" className="bg-yellow-500">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            {t("lowStock") || "Low Stock"}
                          </Badge>
                        ) : (
                          <Badge variant="success" className="bg-green-500">
                            {t("inStock") || "In Stock"}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button 
                            variant="outline" 
                            size="icon"
                            onClick={() => handleUpdateStock(product, "add")}
                            title={t("addStock") || "Add Stock"}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="icon"
                            onClick={() => handleUpdateStock(product, "remove")}
                            title={t("removeStock") || "Remove Stock"}
                            disabled={product.stock === 0}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="icon"
                            onClick={() => handleUpdateStock(product, "set")}
                            title={t("setStock") || "Set Stock"}
                          >
                            <span className="text-xs font-bold">123</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
          
          <div className="mt-6">
            {renderPagination()}
          </div>
        </CardContent>
      </Card>
      
      {selectedProduct && (
        <StockUpdateDialog
          product={selectedProduct}
          updateType={updateType}
          open={isUpdateDialogOpen}
          onOpenChange={setIsUpdateDialogOpen}
          onStockUpdated={handleStockUpdated}
        />
      )}
    </>
  )
}
