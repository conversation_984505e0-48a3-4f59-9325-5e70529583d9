"use client"

import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@components/ui/card"
import { Input } from "@components/ui/input"
import { But<PERSON> } from "@components/ui/button"
import { Badge } from "@components/ui/badge"
import { Checkbox } from "@components/ui/checkbox"
import { Skeleton } from "@components/ui/skeleton"
import { Textarea } from "@components/ui/textarea"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@components/ui/tabs"
import { Search, Upload, Download, Plus, Minus, Check, AlertTriangle } from "lucide-react"
import { useToast } from "@components/ui/use-toast"
import Image from "next/image"

interface Product {
  _id: string
  title: string
  price: number
  stock: number
  minStockThreshold?: number
  category: {
    _id: string
    name: string
  }
  images: string[]
}

interface BatchUpdateItem {
  productId: string
  quantity: number
  actionType: "add" | "remove" | "adjust"
  notes?: string
}

export default function BatchUpdate() {
  const { t } = useTranslation("seller")
  const { toast } = useToast()
  
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [updateType, setUpdateType] = useState<"add" | "remove" | "adjust">("add")
  const [quantity, setQuantity] = useState<number>(1)
  const [notes, setNotes] = useState<string>("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState<string>("select")
  const [csvContent, setCsvContent] = useState<string>("")

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true)
        
        // Build query parameters
        const params = new URLSearchParams()
        params.append("limit", "100") // Get more products for batch update
        
        if (searchQuery) {
          params.append("query", searchQuery)
        }
        
        const response = await fetch(`/api/seller/products?${params.toString()}`)
        
        if (!response.ok) {
          throw new Error("Failed to fetch products")
        }
        
        const data = await response.json()
        setProducts(data.products)
      } catch (error) {
        console.error("Error fetching products:", error)
        setError(error instanceof Error ? error.message : "An unknown error occurred")
      } finally {
        setLoading(false)
      }
    }
    
    fetchProducts()
  }, [searchQuery])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
  }

  const handleSelectAll = () => {
    if (selectedProducts.length === products.length) {
      setSelectedProducts([])
    } else {
      setSelectedProducts(products.map(product => product._id))
    }
  }

  const handleSelectProduct = (productId: string) => {
    if (selectedProducts.includes(productId)) {
      setSelectedProducts(selectedProducts.filter(id => id !== productId))
    } else {
      setSelectedProducts([...selectedProducts, productId])
    }
  }

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value)
    if (!isNaN(value) && value >= 0) {
      setQuantity(value)
    }
  }

  const handleUpdateTypeChange = (value: string) => {
    setUpdateType(value as "add" | "remove" | "adjust")
  }

  const handleSubmit = async () => {
    if (selectedProducts.length === 0) {
      toast({
        title: t("noProductsSelected") || "No Products Selected",
        description: t("pleaseSelectProducts") || "Please select at least one product to update.",
        variant: "destructive",
      })
      return
    }

    if (quantity <= 0) {
      toast({
        title: t("invalidQuantity") || "Invalid Quantity",
        description: t("quantityMustBePositive") || "Quantity must be a positive number.",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)

      // Prepare batch update items
      const items: BatchUpdateItem[] = selectedProducts.map(productId => ({
        productId,
        quantity,
        actionType: updateType,
        notes: notes || undefined,
      }))

      // Call the batch update API
      const response = await fetch("/api/seller/inventory/batch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ items }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update inventory")
      }

      const data = await response.json()

      // Show success message
      toast({
        title: t("inventoryUpdated") || "Inventory Updated",
        description: t("inventoryUpdatedDesc", { count: items.length }) || 
          `Successfully updated inventory for ${items.length} products.`,
      })

      // Reset form
      setSelectedProducts([])
      setQuantity(1)
      setNotes("")

      // Refresh product list
      const refreshResponse = await fetch(`/api/seller/products?limit=100`)
      if (refreshResponse.ok) {
        const refreshData = await refreshResponse.json()
        setProducts(refreshData.products)
      }
    } catch (error) {
      console.error("Error updating inventory:", error)
      
      toast({
        title: t("error") || "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleGenerateCSVTemplate = () => {
    // Generate CSV header
    const header = "product_id,product_name,current_stock,action_type,quantity,notes"
    
    // Generate CSV rows
    const rows = products.map(product => 
      `${product._id},"${product.title}",${product.stock},"add",0,""`
    )
    
    // Combine header and rows
    const csv = [header, ...rows].join("\n")
    
    setCsvContent(csv)
    
    // Create a download link
    const blob = new Blob([csv], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = "inventory_update_template.csv"
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("error") || "Error"}</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => window.location.reload()}>
            {t("tryAgain") || "Try Again"}
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("batchInventoryUpdate") || "Batch Inventory Update"}</CardTitle>
        <CardDescription>
          {t("batchInventoryUpdateDesc") || "Update stock levels for multiple products at once."}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="select">
              {t("selectProducts") || "Select Products"}
            </TabsTrigger>
            <TabsTrigger value="csv">
              {t("csvImport") || "CSV Import"}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="select" className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4 items-end">
              <form onSubmit={handleSearch} className="flex-1">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="search"
                    placeholder={t("searchProducts") || "Search products..."}
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </form>
              
              <div className="w-full sm:w-[180px]">
                <Select value={updateType} onValueChange={handleUpdateTypeChange}>
                  <SelectTrigger>
                    <span>{t("updateType") || "Update Type"}</span>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="add">
                      <div className="flex items-center">
                        <Plus className="h-4 w-4 mr-2" />
                        <span>{t("addStock") || "Add Stock"}</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="remove">
                      <div className="flex items-center">
                        <Minus className="h-4 w-4 mr-2" />
                        <span>{t("removeStock") || "Remove Stock"}</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="adjust">
                      <div className="flex items-center">
                        <span className="text-xs font-bold mr-2">123</span>
                        <span>{t("setStock") || "Set Stock"}</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="flex items-center space-x-4">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-12 w-12 rounded-md" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-[250px]" />
                      <Skeleton className="h-4 w-[200px]" />
                    </div>
                  </div>
                ))}
              </div>
            ) : products.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500">
                  {searchQuery 
                    ? (t("noProductsFound") || "No products found matching your search.") 
                    : (t("noProductsYet") || "You haven't added any products yet.")}
                </p>
              </div>
            ) : (
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">
                        <Checkbox 
                          checked={selectedProducts.length === products.length && products.length > 0}
                          onCheckedChange={handleSelectAll}
                          aria-label="Select all"
                        />
                      </TableHead>
                      <TableHead>{t("product") || "Product"}</TableHead>
                      <TableHead className="text-center">{t("currentStock") || "Current Stock"}</TableHead>
                      <TableHead className="text-center">{t("newStock") || "New Stock"}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {products.map((product) => {
                      const isSelected = selectedProducts.includes(product._id)
                      let newStock = product.stock
                      
                      if (isSelected) {
                        if (updateType === "add") {
                          newStock = product.stock + quantity
                        } else if (updateType === "remove") {
                          newStock = Math.max(0, product.stock - quantity)
                        } else if (updateType === "adjust") {
                          newStock = quantity
                        }
                      }
                      
                      return (
                        <TableRow key={product._id} className={isSelected ? "bg-primary/5" : undefined}>
                          <TableCell>
                            <Checkbox 
                              checked={isSelected}
                              onCheckedChange={() => handleSelectProduct(product._id)}
                              aria-label={`Select ${product.title}`}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <div className="h-10 w-10 relative rounded overflow-hidden bg-gray-100">
                                {product.images && product.images.length > 0 ? (
                                  <Image
                                    src={product.images[0]}
                                    alt={product.title}
                                    fill
                                    className="object-cover"
                                    sizes="40px"
                                  />
                                ) : (
                                  <div className="flex items-center justify-center h-full w-full text-gray-400 text-xs">
                                    No image
                                  </div>
                                )}
                              </div>
                              <div className="font-medium truncate max-w-[200px]">
                                {product.title}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <Badge variant={product.stock > 0 ? "outline" : "destructive"}>
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">
                            {isSelected ? (
                              <Badge variant="secondary" className={newStock > product.stock ? "bg-green-100 text-green-800" : newStock < product.stock ? "bg-red-100 text-red-800" : ""}>
                                {newStock}
                              </Badge>
                            ) : (
                              <span className="text-gray-400">-</span>
                            )}
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
            
            <div className="space-y-4 pt-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="quantity" className="text-sm font-medium">
                    {updateType === "add" 
                      ? (t("quantityToAdd") || "Quantity to Add") 
                      : updateType === "remove" 
                        ? (t("quantityToRemove") || "Quantity to Remove")
                        : (t("newStockLevel") || "New Stock Level")}
                  </label>
                  <Input
                    id="quantity"
                    type="number"
                    min={0}
                    value={quantity}
                    onChange={handleQuantityChange}
                  />
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="notes" className="text-sm font-medium">
                    {t("notes") || "Notes"} <span className="text-gray-400 text-xs">({t("optional") || "Optional"})</span>
                  </label>
                  <Input
                    id="notes"
                    placeholder={t("notesPlaceholder") || "Enter notes about this update"}
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="flex justify-end">
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting || selectedProducts.length === 0}
                >
                  {isSubmitting ? (
                    <>{t("updating") || "Updating..."}</>
                  ) : (
                    <>
                      <Check className="h-4 w-4 mr-2" />
                      {t("updateInventory") || "Update Inventory"}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="csv" className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-blue-800">
                    {t("csvImportInfo") || "CSV Import Information"}
                  </h4>
                  <p className="text-xs text-blue-700 mt-1">
                    {t("csvImportInfoDesc") || 
                      "Download the CSV template, fill in the required information, and upload it to update your inventory in bulk. The CSV file should have the following columns: product_id, product_name, current_stock, action_type, quantity, notes."}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col items-center justify-center py-8 border-2 border-dashed rounded-md">
              <Download className="h-8 w-8 text-gray-400 mb-2" />
              <h3 className="text-lg font-medium mb-2">
                {t("downloadCsvTemplate") || "Download CSV Template"}
              </h3>
              <p className="text-sm text-gray-500 mb-4 text-center max-w-md">
                {t("downloadCsvTemplateDesc") || 
                  "Download a CSV template with your current products to fill in the required information."}
              </p>
              <Button onClick={handleGenerateCSVTemplate}>
                <Download className="h-4 w-4 mr-2" />
                {t("downloadTemplate") || "Download Template"}
              </Button>
            </div>
            
            <div className="flex flex-col items-center justify-center py-8 border-2 border-dashed rounded-md">
              <Upload className="h-8 w-8 text-gray-400 mb-2" />
              <h3 className="text-lg font-medium mb-2">
                {t("uploadCsvFile") || "Upload CSV File"}
              </h3>
              <p className="text-sm text-gray-500 mb-4 text-center max-w-md">
                {t("uploadCsvFileDesc") || 
                  "Upload your completed CSV file to update your inventory in bulk."}
              </p>
              <p className="text-sm text-yellow-500 mb-4 text-center">
                {t("csvUploadComingSoon") || 
                  "CSV upload functionality coming soon. Please use the product selection method for now."}
              </p>
              <Button disabled>
                <Upload className="h-4 w-4 mr-2" />
                {t("uploadCsv") || "Upload CSV"}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
