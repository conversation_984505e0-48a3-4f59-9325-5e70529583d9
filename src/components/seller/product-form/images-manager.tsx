"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { Button } from "@components/ui/button"
import { Card, CardContent } from "@components/ui/card"
import { Input } from "@components/ui/input"
import { Label } from "@components/ui/label"
import { Trash2, Upload, Plus, X, GripVertical, ArrowLeft, ArrowRight } from "lucide-react"
import { useTranslation } from "react-i18next"
import Image from "next/image"
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from "@dnd-kit/core"
import { SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { restrictToVerticalAxis } from "@dnd-kit/modifiers"

interface ImagesManagerProps {
  initialImages?: string[]
  onSave: (images: string[]) => void
  onNext?: () => void
  onPrevious?: () => void
  productId?: string
}

interface SortableImageProps {
  url: string
  id: string
  onRemove: (id: string) => void
}

function SortableImage({ url, id, onRemove }: SortableImageProps) {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id })
  
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }
  
  return (
    <div 
      ref={setNodeRef} 
      style={style} 
      className="flex items-center gap-2 p-2 bg-white border rounded-md group"
    >
      <div {...attributes} {...listeners} className="cursor-grab">
        <GripVertical className="w-5 h-5 text-gray-400" />
      </div>
      <div className="relative w-16 h-16 overflow-hidden rounded-md">
        <Image 
          src={url} 
          alt="Product image" 
          fill 
          className="object-cover"
          sizes="64px"
        />
      </div>
      <div className="flex-1 overflow-hidden">
        <p className="text-sm truncate">{url.split('/').pop()}</p>
      </div>
      <Button 
        variant="ghost" 
        size="icon" 
        className="opacity-0 group-hover:opacity-100 transition-opacity"
        onClick={() => onRemove(id)}
      >
        <Trash2 className="w-4 h-4 text-red-500" />
      </Button>
    </div>
  )
}

export default function ImagesManager({ initialImages = [], onSave, onNext, onPrevious, productId }: ImagesManagerProps) {
  const { t } = useTranslation("seller")
  const [images, setImages] = useState<string[]>(initialImages)
  const [imageUrl, setImageUrl] = useState("")
  const [isUploading, setIsUploading] = useState(false)

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  const onDrop = useCallback((acceptedFiles: File[]) => {
    // In a real implementation, you would upload these files to your server or cloud storage
    // For now, we'll just create object URLs
    const newImages = acceptedFiles.map(file => URL.createObjectURL(file))
    setImages(prev => [...prev, ...newImages])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ 
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    }
  })

  const handleAddImageUrl = () => {
    if (imageUrl && !images.includes(imageUrl)) {
      setImages(prev => [...prev, imageUrl])
      setImageUrl("")
    }
  }

  const handleRemoveImage = (id: string) => {
    setImages(prev => prev.filter((_, index) => index.toString() !== id))
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    
    if (over && active.id !== over.id) {
      const oldIndex = parseInt(active.id.toString())
      const newIndex = parseInt(over.id.toString())
      
      setImages(prev => {
        const newImages = [...prev]
        const [movedItem] = newImages.splice(oldIndex, 1)
        newImages.splice(newIndex, 0, movedItem)
        return newImages
      })
    }
  }

  const handleSave = () => {
    onSave(images)
    if (onNext) onNext()
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-6">
          <div 
            {...getRootProps()} 
            className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
              isDragActive ? 'border-primary bg-primary/5' : 'border-gray-300 hover:border-primary'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="w-10 h-10 mx-auto text-gray-400 mb-2" />
            <p className="text-sm text-gray-600">
              {isDragActive
                ? t("dropImagesToUpload") || "Drop the images here..."
                : t("dragAndDropImages") || "Drag & drop images here, or click to select files"}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              {t("supportedFormats") || "Supported formats: JPEG, PNG, GIF, WebP"}
            </p>
          </div>

          <div className="flex items-end gap-2">
            <div className="flex-1 space-y-2">
              <Label htmlFor="imageUrl">{t("imageUrl") || "Image URL"}</Label>
              <Input
                id="imageUrl"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                placeholder="https://example.com/image.jpg"
              />
            </div>
            <Button 
              type="button" 
              onClick={handleAddImageUrl} 
              disabled={!imageUrl}
              className="mb-px"
            >
              <Plus className="w-4 h-4 mr-1" />
              {t("add") || "Add"}
            </Button>
          </div>

          <div className="space-y-2">
            <h3 className="text-sm font-medium">
              {t("productImages") || "Product Images"} ({images.length})
            </h3>
            
            {images.length > 0 ? (
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
                modifiers={[restrictToVerticalAxis]}
              >
                <SortableContext 
                  items={images.map((_, index) => index.toString())}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-2 max-h-80 overflow-y-auto p-1">
                    {images.map((url, index) => (
                      <SortableImage 
                        key={index} 
                        id={index.toString()} 
                        url={url} 
                        onRemove={handleRemoveImage} 
                      />
                    ))}
                  </div>
                </SortableContext>
              </DndContext>
            ) : (
              <div className="text-center py-8 border border-dashed rounded-md">
                <p className="text-sm text-gray-500">
                  {t("noImagesAdded") || "No images added yet"}
                </p>
              </div>
            )}
          </div>

          <div className="flex justify-between">
            {onPrevious && (
              <Button type="button" variant="outline" onClick={onPrevious}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                {t("previous") || "Previous"}
              </Button>
            )}
            <div className="ml-auto flex gap-2">
              <Button type="button" onClick={handleSave}>
                {onNext ? (
                  <>
                    {t("saveAndContinue") || "Save & Continue"}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </>
                ) : (
                  t("saveImages") || "Save Images"
                )}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
