"use client"

import { format } from "date-fns"
import { useTranslation } from "react-i18next"
import {
  Package,
  Truck,
  CheckCircle,
  XCircle,
  Clock,
  CreditCard,
} from "lucide-react"

interface StatusHistoryItem {
  status: string
  timestamp: string
  note?: string
}

interface OrderTimelineProps {
  statusHistory: StatusHistoryItem[]
}

export function OrderTimeline({ statusHistory }: OrderTimelineProps) {
  const { t } = useTranslation("orders")

  // Sắp xếp lịch sử trạng thái theo thời gian giảm dần (mới nhất lên đầu)
  const sortedHistory = [...statusHistory].sort((a, b) => {
    return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  })

  // Hàm lấy icon dựa trên trạng thái
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-5 w-5" />
      case "processing":
        return <Package className="h-5 w-5" />
      case "shipped":
        return <Truck className="h-5 w-5" />
      case "delivered":
        return <CheckCircle className="h-5 w-5" />
      case "cancelled":
        return <XCircle className="h-5 w-5" />
      case "paid":
        return <CreditCard className="h-5 w-5" />
      default:
        return <Clock className="h-5 w-5" />
    }
  }

  // Hàm lấy màu dựa trên trạng thái
  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "text-yellow-500 bg-yellow-100 dark:bg-yellow-900 dark:bg-opacity-20"
      case "processing":
        return "text-blue-500 bg-blue-100 dark:bg-blue-900 dark:bg-opacity-20"
      case "shipped":
        return "text-indigo-500 bg-indigo-100 dark:bg-indigo-900 dark:bg-opacity-20"
      case "delivered":
        return "text-green-500 bg-green-100 dark:bg-green-900 dark:bg-opacity-20"
      case "cancelled":
        return "text-red-500 bg-red-100 dark:bg-red-900 dark:bg-opacity-20"
      case "paid":
        return "text-emerald-500 bg-emerald-100 dark:bg-emerald-900 dark:bg-opacity-20"
      default:
        return "text-gray-500 bg-gray-100 dark:bg-gray-800"
    }
  }

  // Hàm lấy tên trạng thái
  const getStatusName = (status: string) => {
    switch (status) {
      case "pending":
        return t("pending") || "Chờ xử lý"
      case "processing":
        return t("processing") || "Đang xử lý"
      case "shipped":
        return t("shipped") || "Đang giao hàng"
      case "delivered":
        return t("delivered") || "Đã giao hàng"
      case "cancelled":
        return t("cancelled") || "Đã hủy"
      case "paid":
        return t("paid") || "Đã thanh toán"
      default:
        return status
    }
  }

  if (!statusHistory || statusHistory.length === 0) {
    return (
      <div className="text-center py-6 text-muted-foreground">
        {t("noStatusHistory") || "Không có lịch sử trạng thái"}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {sortedHistory.map((item, index) => (
        <div key={index} className="relative pl-8">
          {/* Đường kẻ dọc kết nối các điểm */}
          {index < sortedHistory.length - 1 && (
            <div className="absolute left-[15px] top-[30px] bottom-0 w-[2px] bg-muted" />
          )}
          
          {/* Icon trạng thái */}
          <div
            className={`absolute left-0 top-0 flex items-center justify-center w-8 h-8 rounded-full ${getStatusColor(
              item.status
            )}`}
          >
            {getStatusIcon(item.status)}
          </div>
          
          {/* Nội dung */}
          <div className="pb-6">
            <div className="flex items-center gap-2">
              <h4 className="font-medium">{getStatusName(item.status)}</h4>
            </div>
            <time className="text-sm text-muted-foreground">
              {format(new Date(item.timestamp), "dd/MM/yyyy HH:mm")}
            </time>
            {item.note && <p className="mt-1 text-sm">{item.note}</p>}
          </div>
        </div>
      ))}
    </div>
  )
}
