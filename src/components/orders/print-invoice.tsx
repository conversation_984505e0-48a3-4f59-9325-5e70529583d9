"use client"

import { useRef } from "react"
import { useReactToPrint } from "react-to-print"
import { format } from "date-fns"
import { useTranslation } from "react-i18next"
import { Printer } from "lucide-react"
import { Button } from "@components/ui/button"

interface PrintInvoiceProps {
  order: any
}

export function PrintInvoice({ order }: PrintInvoiceProps) {
  const { t } = useTranslation("orders")
  const componentRef = useRef<HTMLDivElement>(null)

  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: `Invoice-${order.orderNumber || order._id}`,
    pageStyle: `
      @page {
        size: A4;
        margin: 10mm;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
      }
    `,
  })

  return (
    <>
      <Button variant="outline" size="sm" onClick={handlePrint}>
        <Printer className="h-4 w-4 mr-2" />
        {t("printInvoice") || "In hóa đơn"}
      </Button>

      <div className="hidden">
        <div ref={componentRef} className="p-8 max-w-[210mm] mx-auto bg-white text-black">
          {/* Header */}
          <div className="flex justify-between items-start mb-8">
            <div>
              <h1 className="text-2xl font-bold">ZenBuy</h1>
              <p className="text-sm">123 Đường ABC, Quận 1, TP.HCM</p>
              <p className="text-sm">Email: <EMAIL></p>
              <p className="text-sm">Điện thoại: 1900 1234</p>
            </div>
            <div className="text-right">
              <h2 className="text-xl font-bold">{t("invoice") || "HÓA ĐƠN"}</h2>
              <p className="text-sm">
                <span className="font-medium">{t("invoiceNumber") || "Số hóa đơn"}:</span> {order.orderNumber || order._id}
              </p>
              <p className="text-sm">
                <span className="font-medium">{t("date") || "Ngày"}:</span>{" "}
                {order.createdAt ? format(new Date(order.createdAt), "dd/MM/yyyy") : "N/A"}
              </p>
            </div>
          </div>

          {/* Customer Info */}
          <div className="grid grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="font-bold mb-2">{t("billTo") || "Thông tin khách hàng"}</h3>
              <p className="text-sm">{order.user?.name || "N/A"}</p>
              <p className="text-sm">{order.user?.email || "N/A"}</p>
            </div>
            <div>
              <h3 className="font-bold mb-2">{t("shipTo") || "Địa chỉ giao hàng"}</h3>
              <p className="text-sm">{order.shippingAddress?.fullName || "N/A"}</p>
              <p className="text-sm">{order.shippingAddress?.address || "N/A"}</p>
              <p className="text-sm">
                {order.shippingAddress?.city || "N/A"}, {order.shippingAddress?.state || "N/A"}
              </p>
              <p className="text-sm">{order.shippingAddress?.country || "N/A"}, {order.shippingAddress?.zipCode || "N/A"}</p>
              <p className="text-sm">{order.shippingAddress?.phone || "N/A"}</p>
            </div>
          </div>

          {/* Order Info */}
          <div className="mb-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm">
                  <span className="font-medium">{t("orderNumber") || "Mã đơn hàng"}:</span> {order.orderNumber || order._id}
                </p>
                <p className="text-sm">
                  <span className="font-medium">{t("orderDate") || "Ngày đặt hàng"}:</span>{" "}
                  {order.createdAt ? format(new Date(order.createdAt), "dd/MM/yyyy") : "N/A"}
                </p>
              </div>
              <div>
                <p className="text-sm">
                  <span className="font-medium">{t("paymentMethod") || "Phương thức thanh toán"}:</span>{" "}
                  {order.paymentMethod === "credit_card"
                    ? t("creditCard") || "Thẻ tín dụng"
                    : order.paymentMethod === "bank_transfer"
                    ? t("bankTransfer") || "Chuyển khoản ngân hàng"
                    : t("cashOnDelivery") || "Thanh toán khi nhận hàng"}
                </p>
                <p className="text-sm">
                  <span className="font-medium">{t("shippingMethod") || "Phương thức vận chuyển"}:</span>{" "}
                  {order.shippingMethod === "standard"
                    ? t("standardShipping") || "Vận chuyển tiêu chuẩn"
                    : order.shippingMethod === "express"
                    ? t("expressShipping") || "Vận chuyển nhanh"
                    : t("pickup") || "Nhận tại cửa hàng"}
                </p>
              </div>
            </div>
          </div>

          {/* Items Table */}
          <table className="w-full mb-8 border-collapse">
            <thead>
              <tr className="bg-gray-100">
                <th className="py-2 px-4 text-left border border-gray-300">{t("product") || "Sản phẩm"}</th>
                <th className="py-2 px-4 text-center border border-gray-300">{t("quantity") || "Số lượng"}</th>
                <th className="py-2 px-4 text-right border border-gray-300">{t("unitPrice") || "Đơn giá"}</th>
                <th className="py-2 px-4 text-right border border-gray-300">{t("amount") || "Thành tiền"}</th>
              </tr>
            </thead>
            <tbody>
              {order.items?.map((item: any, index: number) => (
                <tr key={index}>
                  <td className="py-2 px-4 border border-gray-300">{item.product?.title || "Unknown Product"}</td>
                  <td className="py-2 px-4 text-center border border-gray-300">{item.quantity}</td>
                  <td className="py-2 px-4 text-right border border-gray-300">
                    {new Intl.NumberFormat("vi-VN", {
                      style: "currency",
                      currency: "VND",
                    }).format(item.price)}
                  </td>
                  <td className="py-2 px-4 text-right border border-gray-300">
                    {new Intl.NumberFormat("vi-VN", {
                      style: "currency",
                      currency: "VND",
                    }).format(item.price * item.quantity)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Totals */}
          <div className="flex justify-end mb-8">
            <div className="w-64">
              <div className="flex justify-between py-2">
                <span className="font-medium">{t("subtotal") || "Tạm tính"}:</span>
                <span>
                  {new Intl.NumberFormat("vi-VN", {
                    style: "currency",
                    currency: "VND",
                  }).format(order.totalAmount || 0)}
                </span>
              </div>
              <div className="flex justify-between py-2">
                <span className="font-medium">{t("shipping") || "Phí vận chuyển"}:</span>
                <span>
                  {new Intl.NumberFormat("vi-VN", {
                    style: "currency",
                    currency: "VND",
                  }).format(order.shippingFee || 0)}
                </span>
              </div>
              <div className="flex justify-between py-2">
                <span className="font-medium">{t("tax") || "Thuế"}:</span>
                <span>
                  {new Intl.NumberFormat("vi-VN", {
                    style: "currency",
                    currency: "VND",
                  }).format(order.tax || 0)}
                </span>
              </div>
              <div className="flex justify-between py-2 border-t border-gray-300 font-bold">
                <span>{t("total") || "Tổng cộng"}:</span>
                <span>
                  {new Intl.NumberFormat("vi-VN", {
                    style: "currency",
                    currency: "VND",
                  }).format(order.finalAmount || 0)}
                </span>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center text-sm mt-8 pt-8 border-t border-gray-300">
            <p>{t("thankYou") || "Cảm ơn bạn đã mua hàng tại ZenBuy!"}</p>
            <p className="mt-2">
              {t("questions") || "Nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ"}: <EMAIL>
            </p>
          </div>
        </div>
      </div>
    </>
  )
}
