"use client"

import { useTranslation } from "react-i18next"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@components/ui/table"
import { Info } from "lucide-react"

interface ProductSpecificationsProps {
  specifications: Record<string, string> | null | undefined
}

export default function ProductSpecifications({ specifications }: ProductSpecificationsProps) {
  const { t } = useTranslation("detail-product")

  // If no specifications, show placeholder
  if (!specifications || Object.keys(specifications).length === 0) {
    return (
      <div className="py-4">
        <div className="flex items-center justify-center p-6 text-center border border-dashed rounded-lg">
          <div className="flex flex-col items-center">
            <Info className="w-10 h-10 text-gray-400 mb-2" />
            <p className="text-gray-500">{t("noSpecifications") || "No specifications available for this product"}</p>
          </div>
        </div>
      </div>
    )
  }

  // Group specifications by category if they follow the format "category.name"
  const groupedSpecs: Record<string, Record<string, string>> = {}
  const ungroupedSpecs: Record<string, string> = {}

  Object.entries(specifications).forEach(([key, value]) => {
    const parts = key.split('.')
    if (parts.length === 2) {
      const [category, name] = parts
      if (!groupedSpecs[category]) {
        groupedSpecs[category] = {}
      }
      groupedSpecs[category][name] = value
    } else {
      ungroupedSpecs[key] = value
    }
  })

  const hasGroupedSpecs = Object.keys(groupedSpecs).length > 0

  return (
    <div className="py-4">
      {/* Ungrouped specifications */}
      {Object.keys(ungroupedSpecs).length > 0 && (
        <div className="mb-6">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-1/3">{t("specification") || "Specification"}</TableHead>
                <TableHead>{t("value") || "Value"}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Object.entries(ungroupedSpecs).map(([key, value]) => (
                <TableRow key={key}>
                  <TableCell className="font-medium">{key}</TableCell>
                  <TableCell>{value || "-"}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Grouped specifications */}
      {hasGroupedSpecs && Object.entries(groupedSpecs).map(([category, specs]) => (
        <div key={category} className="mb-6">
          <h3 className="text-lg font-medium mb-3 capitalize">{category}</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-1/3">{t("specification") || "Specification"}</TableHead>
                <TableHead>{t("value") || "Value"}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Object.entries(specs).map(([key, value]) => (
                <TableRow key={key}>
                  <TableCell className="font-medium">{key}</TableCell>
                  <TableCell>{value || "-"}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ))}
    </div>
  )
}
