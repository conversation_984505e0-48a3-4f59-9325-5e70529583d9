"use client"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import { 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut, 
  X, 
  Maximize2, 
  Minimize2 
} from "lucide-react"
import { But<PERSON> } from "@components/ui/button"
import { Dialog, DialogContent, DialogTrigger } from "@components/ui/dialog"
import { cn } from "@/lib/utils"

interface ImageGalleryProps {
  images: string[]
  productTitle: string
}

export default function ImageGallery({ images, productTitle }: ImageGalleryProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isZoomed, setIsZoomed] = useState(false)
  const [zoomLevel, setZoomLevel] = useState(1)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  
  const imageContainerRef = useRef<HTMLDivElement>(null)
  const zoomedImageRef = useRef<HTMLDivElement>(null)

  // Reset zoom and position when changing images
  useEffect(() => {
    setIsZoomed(false)
    setZoomLevel(1)
    setPosition({ x: 0, y: 0 })
  }, [currentIndex])

  // Handle mouse move for panning when zoomed
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isZoomed || !zoomedImageRef.current) return

    const { left, top, width, height } = zoomedImageRef.current.getBoundingClientRect()
    
    // Calculate relative position (0 to 1)
    const relativeX = (e.clientX - left) / width
    const relativeY = (e.clientY - top) / height
    
    // Calculate position based on zoom level
    const maxX = (zoomLevel - 1) * 100
    const maxY = (zoomLevel - 1) * 100
    
    // Invert the position calculation to make the image move in the direction of the mouse
    const newX = -maxX * relativeX
    const newY = -maxY * relativeY
    
    setPosition({ 
      x: Math.max(-maxX, Math.min(0, newX)), 
      y: Math.max(-maxY, Math.min(0, newY)) 
    })
  }

  const handleZoomIn = () => {
    if (zoomLevel < 3) {
      setZoomLevel(prev => prev + 0.5)
      setIsZoomed(true)
    }
  }

  const handleZoomOut = () => {
    if (zoomLevel > 1) {
      setZoomLevel(prev => prev - 0.5)
      if (zoomLevel - 0.5 <= 1) {
        setIsZoomed(false)
        setPosition({ x: 0, y: 0 })
      }
    }
  }

  const toggleZoom = () => {
    if (isZoomed) {
      setIsZoomed(false)
      setZoomLevel(1)
      setPosition({ x: 0, y: 0 })
    } else {
      setIsZoomed(true)
      setZoomLevel(2)
    }
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length)
  }

  const prevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
  }

  const handleThumbnailClick = (index: number) => {
    setCurrentIndex(index)
  }

  // If no images, show placeholder
  if (!images || images.length === 0) {
    return (
      <div className="relative aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
        <span className="text-gray-400">No images available</span>
      </div>
    )
  }

  const renderGalleryContent = (inDialog = false) => (
    <div className={cn(
      "flex flex-col",
      inDialog ? "h-full" : "h-auto"
    )}>
      {/* Main image container */}
      <div 
        ref={imageContainerRef}
        className={cn(
          "relative overflow-hidden rounded-lg",
          inDialog ? "flex-1" : "aspect-square",
          isZoomed ? "cursor-move" : "cursor-zoom-in"
        )}
        onClick={toggleZoom}
        onMouseMove={handleMouseMove}
      >
        <div
          ref={zoomedImageRef}
          className="relative h-full w-full transition-transform duration-200"
          style={{
            transform: isZoomed ? `scale(${zoomLevel}) translate(${position.x}%, ${position.y}%)` : 'none'
          }}
        >
          <Image
            src={images[currentIndex]}
            alt={`${productTitle} - Image ${currentIndex + 1}`}
            fill
            className="object-contain"
            priority={currentIndex === 0}
            sizes={inDialog ? "100vw" : "(max-width: 768px) 100vw, 50vw"}
          />
        </div>

        {/* Navigation arrows */}
        {images.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white/90 rounded-full"
              onClick={(e) => {
                e.stopPropagation()
                prevImage()
              }}
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white/90 rounded-full"
              onClick={(e) => {
                e.stopPropagation()
                nextImage()
              }}
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
          </>
        )}

        {/* Zoom controls */}
        <div 
          className="absolute bottom-2 right-2 flex space-x-2"
          onClick={(e) => e.stopPropagation()}
        >
          <Button
            variant="outline"
            size="icon"
            className="bg-white/80 hover:bg-white/90 rounded-full h-8 w-8"
            onClick={handleZoomOut}
            disabled={zoomLevel <= 1}
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="bg-white/80 hover:bg-white/90 rounded-full h-8 w-8"
            onClick={handleZoomIn}
            disabled={zoomLevel >= 3}
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
          {inDialog && (
            <Button
              variant="outline"
              size="icon"
              className="bg-white/80 hover:bg-white/90 rounded-full h-8 w-8"
              onClick={toggleFullscreen}
            >
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
          )}
        </div>
      </div>

      {/* Thumbnails */}
      {images.length > 1 && (
        <div className="grid grid-cols-5 gap-2 mt-4">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => handleThumbnailClick(index)}
              className={cn(
                "relative aspect-square rounded-md overflow-hidden",
                currentIndex === index ? "ring-2 ring-primary" : "ring-1 ring-gray-200"
              )}
            >
              <Image
                src={image}
                alt={`${productTitle} - Thumbnail ${index + 1}`}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 20vw, 10vw"
              />
            </button>
          ))}
        </div>
      )}
    </div>
  )

  return (
    <div>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <div className="cursor-pointer">
            {renderGalleryContent(false)}
          </div>
        </DialogTrigger>
        <DialogContent className={cn(
          "max-w-screen-lg p-0 w-[90vw] h-[90vh]",
          isFullscreen ? "fixed inset-0 w-screen h-screen max-w-none rounded-none" : ""
        )}>
          <div className="relative h-full p-4">
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-2 z-10"
              onClick={() => setIsDialogOpen(false)}
            >
              <X className="h-4 w-4" />
            </Button>
            {renderGalleryContent(true)}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
