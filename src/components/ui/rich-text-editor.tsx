"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@components/ui/tabs"
import { 
  Bold, Italic, Underline, List, ListOrdered, 
  AlignLeft, AlignCenter, AlignRight, Link, Image as ImageIcon,
  Heading1, Heading2, Heading3, Code, Quote, Undo, Redo
} from "lucide-react"
import { cn } from "@lib/utils"

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  minHeight?: string
  className?: string
}

export default function RichTextEditor({ 
  value, 
  onChange, 
  placeholder = "Enter rich text content...",
  minHeight = "200px",
  className
}: RichTextEditorProps) {
  const [activeTab, setActiveTab] = useState<"edit" | "preview">("edit")
  const editorRef = useRef<HTMLDivElement>(null)
  
  // Initialize editor content
  useEffect(() => {
    if (editorRef.current && value) {
      editorRef.current.innerHTML = value
    }
  }, [])

  // Handle content changes
  const handleContentChange = () => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML)
    }
  }

  // Execute command on the document
  const execCommand = (command: string, value: string = "") => {
    document.execCommand(command, false, value)
    handleContentChange()
    editorRef.current?.focus()
  }

  // Handle link insertion
  const insertLink = () => {
    const url = prompt("Enter URL:")
    if (url) {
      execCommand("createLink", url)
    }
  }

  // Handle image insertion
  const insertImage = () => {
    const url = prompt("Enter image URL:")
    if (url) {
      execCommand("insertImage", url)
    }
  }

  return (
    <div className={cn("border rounded-md overflow-hidden", className)}>
      <Tabs defaultValue="edit" value={activeTab} onValueChange={(v) => setActiveTab(v as "edit" | "preview")}>
        <div className="flex items-center justify-between border-b px-3 py-2">
          <TabsList className="grid w-40 grid-cols-2">
            <TabsTrigger value="edit">Edit</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>
          
          {activeTab === "edit" && (
            <div className="flex items-center space-x-1">
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => execCommand("undo")}
                title="Undo"
              >
                <Undo className="h-4 w-4" />
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => execCommand("redo")}
                title="Redo"
              >
                <Redo className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
        
        {activeTab === "edit" && (
          <div className="border-b px-3 py-1 flex flex-wrap gap-1">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("bold")}
              title="Bold"
            >
              <Bold className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("italic")}
              title="Italic"
            >
              <Italic className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("underline")}
              title="Underline"
            >
              <Underline className="h-4 w-4" />
            </Button>
            <div className="w-px h-6 bg-gray-200 mx-1" />
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("formatBlock", "<h1>")}
              title="Heading 1"
            >
              <Heading1 className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("formatBlock", "<h2>")}
              title="Heading 2"
            >
              <Heading2 className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("formatBlock", "<h3>")}
              title="Heading 3"
            >
              <Heading3 className="h-4 w-4" />
            </Button>
            <div className="w-px h-6 bg-gray-200 mx-1" />
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("insertUnorderedList")}
              title="Bullet List"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("insertOrderedList")}
              title="Numbered List"
            >
              <ListOrdered className="h-4 w-4" />
            </Button>
            <div className="w-px h-6 bg-gray-200 mx-1" />
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("justifyLeft")}
              title="Align Left"
            >
              <AlignLeft className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("justifyCenter")}
              title="Align Center"
            >
              <AlignCenter className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("justifyRight")}
              title="Align Right"
            >
              <AlignRight className="h-4 w-4" />
            </Button>
            <div className="w-px h-6 bg-gray-200 mx-1" />
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={insertLink}
              title="Insert Link"
            >
              <Link className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={insertImage}
              title="Insert Image"
            >
              <ImageIcon className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("formatBlock", "<pre>")}
              title="Code Block"
            >
              <Code className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => execCommand("formatBlock", "<blockquote>")}
              title="Quote"
            >
              <Quote className="h-4 w-4" />
            </Button>
          </div>
        )}
        
        <TabsContent value="edit" className="p-0 m-0">
          <div
            ref={editorRef}
            contentEditable
            className="p-4 outline-none"
            style={{ minHeight }}
            onInput={handleContentChange}
            dangerouslySetInnerHTML={{ __html: value }}
            placeholder={placeholder}
          />
        </TabsContent>
        
        <TabsContent value="preview" className="p-4 m-0 prose max-w-none" style={{ minHeight }}>
          {value ? (
            <div dangerouslySetInnerHTML={{ __html: value }} />
          ) : (
            <p className="text-gray-400 italic">{placeholder}</p>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
