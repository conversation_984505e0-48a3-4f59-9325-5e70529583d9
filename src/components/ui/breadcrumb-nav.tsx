"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronRight, Home } from "lucide-react"
import { cn } from "@/lib/utils"
import { useTranslation } from "react-i18next"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@components/ui/breadcrumb"

export interface BreadcrumbNavItem {
  label: string
  href?: string
  icon?: React.ReactNode
}

interface BreadcrumbNavProps {
  items?: BreadcrumbNavItem[]
  separator?: React.ReactNode
  className?: string
  homeHref?: string
  showHome?: boolean
  autoGenerate?: boolean
}

/**
 * Component hiển thị breadcrumb navigation
 * 
 * @param props Props của component
 * @returns Component breadcrumb
 */
export function BreadcrumbNav({
  items = [],
  separator = <ChevronRight className="h-4 w-4" />,
  className,
  homeHref = "/",
  showHome = true,
  autoGenerate = false,
}: BreadcrumbNavProps) {
  const { t } = useTranslation("common")
  const pathname = usePathname()
  
  // Tự động tạo breadcrumb từ pathname
  const breadcrumbItems = React.useMemo(() => {
    if (!autoGenerate) {
      return items
    }
    
    // Loại bỏ locale từ pathname
    const pathWithoutLocale = pathname.split('/').slice(2).join('/')
    
    // Tạo mảng các phần của đường dẫn
    const pathSegments = pathWithoutLocale.split('/').filter(Boolean)
    
    // Tạo breadcrumb items
    return pathSegments.map((segment, index) => {
      // Tạo đường dẫn tích lũy
      const href = `/${pathSegments.slice(0, index + 1).join('/')}`
      
      // Tạo label từ segment
      const label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
      
      return { label, href }
    })
  }, [pathname, autoGenerate, items])
  
  // Thêm trang chủ vào đầu breadcrumb
  const allItems = React.useMemo(() => {
    if (!showHome) {
      return breadcrumbItems
    }
    
    return [
      { label: t("breadcrumb.home"), href: homeHref, icon: <Home className="h-4 w-4" /> },
      ...breadcrumbItems,
    ]
  }, [breadcrumbItems, showHome, homeHref, t])

  if (allItems.length <= 1 && !showHome) {
    return null
  }

  return (
    <Breadcrumb className={className}>
      <BreadcrumbList>
        {allItems.map((item, index) => (
          <React.Fragment key={index}>
            {index > 0 && <BreadcrumbSeparator>{separator}</BreadcrumbSeparator>}
            <BreadcrumbItem>
              {item.href && index < allItems.length - 1 ? (
                <BreadcrumbLink asChild>
                  <Link href={item.href} className="flex items-center">
                    {item.icon && <span className="mr-1">{item.icon}</span>}
                    {item.label}
                  </Link>
                </BreadcrumbLink>
              ) : (
                <BreadcrumbPage className="flex items-center">
                  {item.icon && <span className="mr-1">{item.icon}</span>}
                  {item.label}
                </BreadcrumbPage>
              )}
            </BreadcrumbItem>
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
