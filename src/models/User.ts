import mongoose, { Schema, Document } from 'mongoose';
import bcrypt from 'bcryptjs';

interface UserPreferences {
  newsletter: boolean;
  notifications: boolean;
  orderNotifications: boolean;
  inventoryNotifications: boolean;
  paymentNotifications: boolean;
  systemNotifications: boolean;
  language: string;
}

export interface IUser extends Document {
  name: string;
  email: string;
  password: string;
  role: 'customer' | 'seller' | 'admin';
  avatar?: string;
  phone?: string;
  bio?: string;
  preferences?: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const UserSchema = new Schema(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    role: { type: String, enum: ['customer', 'seller', 'admin'], default: 'customer' },
    avatar: { type: String },
    phone: { type: String },
    bio: { type: String },
    preferences: {
      newsletter: { type: Boolean, default: true },
      notifications: { type: Boolean, default: true },
      orderNotifications: { type: Boolean, default: true },
      inventoryNotifications: { type: Boolean, default: true },
      paymentNotifications: { type: Bo<PERSON>an, default: true },
      systemNotifications: { type: Boolean, default: true },
      language: { type: String, default: 'vi' },
    },
  },
  { timestamps: true }
);

// Hash password before saving
UserSchema.pre('save', async function (next) {
  const user = this as IUser;
  if (!user.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(user.password, salt);
    next();
  } catch (error: any) {
    next(error);
  }
});

// Method to compare password
UserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
