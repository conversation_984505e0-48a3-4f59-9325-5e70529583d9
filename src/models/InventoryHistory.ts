import mongoose, { Schema, Document } from 'mongoose';

export type InventoryActionType = 'add' | 'remove' | 'adjust' | 'threshold_update';

export interface IInventoryHistory extends Document {
  product: mongoose.Types.ObjectId;
  shop: mongoose.Types.ObjectId;
  user: mongoose.Types.ObjectId;
  actionType: InventoryActionType;
  quantity: number;
  previousQuantity: number;
  newQuantity: number;
  notes: string;
  createdAt: Date;
  updatedAt: Date;
}

const InventoryHistorySchema = new Schema(
  {
    product: { type: Schema.Types.ObjectId, ref: 'Product', required: true },
    shop: { type: Schema.Types.ObjectId, ref: 'Shop', required: true },
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    actionType: { 
      type: String, 
      enum: ['add', 'remove', 'adjust', 'threshold_update'], 
      required: true 
    },
    quantity: { type: Number, required: true },
    previousQuantity: { type: Number, required: true },
    newQuantity: { type: Number, required: true },
    notes: { type: String },
  },
  { timestamps: true }
);

export default mongoose.models.InventoryHistory || 
  mongoose.model<IInventoryHistory>('InventoryHistory', InventoryHistorySchema);
