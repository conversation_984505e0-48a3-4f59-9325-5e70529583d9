import mongoose, { Schema, Document } from 'mongoose';
import type { IUser } from './User';
import type { IProduct } from './Product';
import type { IShippingAddress } from './ShippingAddress';

export interface OrderItem {
  product: IProduct['_id'];
  quantity: number;
  price: number;
  variant?: string;
  attributes?: Record<string, string>;
}

export interface OrderStatus {
  status: string;
  timestamp: Date;
  note?: string;
}

export interface IOrder extends Document {
  orderNumber: string;
  user: IUser['_id'];
  items: OrderItem[];
  totalAmount: number;
  shippingFee: number;
  discount: number;
  tax: number;
  finalAmount: number;
  couponDiscount?: number;
  couponCode?: string;
  paymentMethod: 'credit_card' | 'bank_transfer' | 'cash' | 'cod';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'returned';
  statusHistory: OrderStatus[];
  shippingAddress: IShippingAddress['_id'] | Record<string, any>;
  shippingMethod: string;
  trackingNumber?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

const OrderItemSchema = new Schema({
  product: { type: Schema.Types.ObjectId, ref: 'Product', required: true },
  quantity: { type: Number, required: true, min: 1 },
  price: { type: Number, required: true },
  variant: { type: String },
  attributes: { type: Map, of: String },
});

const OrderStatusSchema = new Schema({
  status: { type: String, required: true },
  timestamp: { type: Date, default: Date.now },
  note: { type: String },
});

const OrderSchema = new Schema(
  {
    orderNumber: { type: String, unique: true, sparse: true },
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    items: [OrderItemSchema],
    totalAmount: { type: Number, required: true },
    shippingFee: { type: Number, default: 0 },
    discount: { type: Number, default: 0 },
    tax: { type: Number, default: 0 },
    finalAmount: { type: Number, required: true },
    couponDiscount: { type: Number },
    couponCode: { type: String },
    paymentMethod: {
      type: String,
      enum: ['credit_card', 'bank_transfer', 'cash', 'cod'],
      required: true
    },
    paymentStatus: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'refunded'],
      default: 'pending',
    },
    status: {
      type: String,
      enum: ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'],
      default: 'pending'
    },
    statusHistory: [OrderStatusSchema],
    shippingAddress: {
      type: Schema.Types.Mixed,
      required: true,
      // Có thể là ObjectId tham chiếu đến ShippingAddress hoặc embedded document
    },
    shippingMethod: { type: String, required: true, default: 'standard' },
    trackingNumber: { type: String },
    notes: { type: String },
  },
  { timestamps: true }
);

// Tạo orderNumber tự động trước khi lưu
OrderSchema.pre('save', async function (next) {
  const order = this as IOrder;

  // Nếu đã có orderNumber, không cần tạo mới
  if (order.orderNumber) {
    return next();
  }

  try {
    // Tạo orderNumber dựa trên timestamp và random string
    const timestamp = new Date().getTime().toString().slice(-6);
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    order.orderNumber = `ZB${timestamp}${random}`;

    // Thêm trạng thái đầu tiên vào lịch sử
    if (!order.statusHistory || order.statusHistory.length === 0) {
      if (!order.statusHistory) {
        order.statusHistory = [];
      }
      order.statusHistory.push({
        status: order.status,
        timestamp: new Date(),
        note: 'Đơn hàng được tạo',
      } as OrderStatus);
    }

    next();
  } catch (error) {
    next(error as Error);
  }
});

// Cập nhật lịch sử trạng thái khi trạng thái thay đổi
OrderSchema.pre('findOneAndUpdate', async function (next) {
  const update = this.getUpdate() as any;

  // Nếu có cập nhật trạng thái
  if (update && update.$set && update.$set.status) {
    const newStatus = update.$set.status;

    // Thêm vào lịch sử trạng thái
    if (!update.$push) {
      update.$push = {};
    }

    update.$push.statusHistory = {
      status: newStatus,
      timestamp: new Date(),
      note: update.$set.statusNote || `Trạng thái đơn hàng thay đổi thành ${newStatus}`,
    };

    // Xóa statusNote khỏi update để không lưu vào document
    if (update.$set.statusNote) {
      delete update.$set.statusNote;
    }
  }

  next();
});

export default mongoose.models.Order || mongoose.model<IOrder>('Order', OrderSchema);
