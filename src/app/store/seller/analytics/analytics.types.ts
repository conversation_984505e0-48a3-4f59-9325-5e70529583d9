import type { DailySales, ProductSales } from "src/types";

export interface PaymentMethodDistribution {
  credit_card: number;
  bank_transfer: number;
  cash: number;
}

export interface OrderStatusDistribution {
  pending: number;
  processing: number;
  shipped: number;
  delivered: number;
  cancelled: number;
}

export interface CouponUsage {
  rate: number;
  averageDiscount: number;
  totalDiscount: number;
  ordersWithCoupons: number;
}

export interface CategoryRevenue {
  category: string;
  revenue: number;
}

export interface AdvancedKpis {
  averageRevenuePerCustomer: number;
  repeatPurchaseRate: number;
  categoryRevenue: CategoryRevenue[];
}

export interface AnalyticsMetadata {
  timeFrame: string;
  comparison: string;
  category?: string;
  startDate: string;
  endDate: string;
  generatedAt: string;
}

export interface AnalyticsState {
  dailySales: DailySales[];
  productSales: ProductSales[];
  totalRevenue: number;
  revenueChange: number;
  totalOrders: number;
  ordersChange: number;
  productsCount: number;
  activeCustomers: number;
  averageOrderValue: number;
  conversionRate: number;
  averageItemsPerOrder: number;
  paymentMethods: PaymentMethodDistribution;
  orderStatuses: OrderStatusDistribution;
  couponUsage: CouponUsage;
  advancedKpis?: AdvancedKpis;
  metadata?: AnalyticsMetadata;
  timeFrame: "day" | "3days" | "week" | "month" | "year";
  isLoading: boolean;
  error: string | null;
}

