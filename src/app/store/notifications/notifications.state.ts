import { hookstate, useHookstate } from "@hookstate/core"
import type { Notification, NotificationSettings } from "@services/notifications.service"

interface NotificationsState {
  notifications: Notification[]
  unreadCount: number
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
  settings: NotificationSettings
}

// Khởi tạo state ban đầu
const initialState: NotificationsState = {
  notifications: [],
  unreadCount: 0,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  },
  settings: {
    notifications: true,
    orderNotifications: true,
    inventoryNotifications: true,
    paymentNotifications: true,
    systemNotifications: true,
  },
}

// Tạo global state
const notificationsState = hookstate<NotificationsState>(initialState)

// Hook để sử dụng state
export const useNotificationsState = () => useHookstate(notificationsState)
