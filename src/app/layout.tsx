import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import './globals.css'
import { ReactNode } from "react"
import { cookies } from "next/headers";
import TranslationsProvider from "../components/providers/translation-provider"
import i18nConfig from "i18nConfig"
import initTranslations from "./i18n"
import { ReactQueryProvider } from "@components/providers/react-query-provider"
import { SuspenseWrapper } from "@components/providers/suspense-wrapper"
import { getAvailableNamespaces } from "@lib/i18n-utils"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Zenera",
  description: "Your one-stop shop for all things trendy and essential",
  generator: 'v0.dev',
  icons: "/favicon.png",
}

export function generateStaticParams() {
  return i18nConfig.locales.map((locale) => ({ locale }));
}
interface RootLayoutProps {
  children: ReactNode;
  params: {
    locale: string;
  };
}

// Tự động lấy tất cả các namespace từ thư mục locales
const i18nNamespaces = getAvailableNamespaces();

export default async function RootLayout({
  children,
  params
}: RootLayoutProps) {
  const cookieStore = cookies();
  const locale = (await cookieStore).get('NEXT_LOCALE')?.value || params.locale;
  const { resources  } = await initTranslations(locale, i18nNamespaces);

  return (
    <html lang={locale} >
      <body className={inter.className}>
        <ReactQueryProvider>
          <SuspenseWrapper>
            <div className="flex flex-col min-h-screen">
              <TranslationsProvider
                locale={locale}
                namespaces={i18nNamespaces}
                resources={resources}
              >
                <main className="flex-grow">{children}</main>
              </TranslationsProvider>
            </div>
          </SuspenseWrapper>
        </ReactQueryProvider>
      </body>
    </html>
  )
}
