"use client"

import { useState } from "react"
import { useTranslation } from "react-i18next"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@components/ui/tabs"
import { Package, History, AlertTriangle, Upload } from "lucide-react"
import InventoryOverview from "@components/seller/inventory/overview"
import InventoryHistory from "@components/seller/inventory/history"
import LowStockAlerts from "@components/seller/inventory/low-stock-alerts"
import BatchUpdate from "@components/seller/inventory/batch-update"

export default function InventoryPage() {
  const { t } = useTranslation("seller")
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">
          {t("inventoryManagement") || "Inventory Management"}
        </h2>
        <p className="text-muted-foreground">
          {t("inventoryManagementDesc") || "Manage your product stock, track inventory changes, and set low stock alerts."}
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            <span className="hidden sm:inline">{t("overview") || "Overview"}</span>
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            <span className="hidden sm:inline">{t("history") || "History"}</span>
          </TabsTrigger>
          <TabsTrigger value="low-stock" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            <span className="hidden sm:inline">{t("lowStock") || "Low Stock"}</span>
          </TabsTrigger>
          <TabsTrigger value="batch-update" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            <span className="hidden sm:inline">{t("batchUpdate") || "Batch Update"}</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <InventoryOverview />
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <InventoryHistory />
        </TabsContent>

        <TabsContent value="low-stock" className="space-y-4">
          <LowStockAlerts />
        </TabsContent>

        <TabsContent value="batch-update" className="space-y-4">
          <BatchUpdate />
        </TabsContent>
      </Tabs>
    </div>
  )
}
