"use client"

import { useState } from "react"
import { useQ<PERSON>y } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { useTranslation } from "react-i18next"
import { format } from "date-fns"
import {
  Package,
  Truck,
  CheckCircle,
  XCircle,
  Clock,
  ChevronRight,
  Search,
  Filter
} from "lucide-react"

import { Button } from "@components/ui/button"
import { Input } from "@components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@components/ui/table"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@components/ui/pagination"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select"
import { Badge } from "@components/ui/badge"
import { Skeleton } from "@components/ui/skeleton"
import { Card, CardContent, CardHeader, CardTitle } from "@components/ui/card"
import { Tabs, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@components/ui/tabs"

// Tạo service cho seller orders
const sellerOrdersService = {
  getOrders: async (params?: { page?: number; limit?: number; status?: string }) => {
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.status) queryParams.append("status", params.status);

    const response = await fetch(`/api/seller/orders?${queryParams.toString()}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch seller orders");
    }

    return response.json();
  },
};

// Status badge component
const OrderStatusBadge = ({ status }: { status: string }) => {
  let variant: "default" | "secondary" | "destructive" | "outline" = "default";
  let icon = null;

  switch (status) {
    case "pending":
      variant = "outline";
      icon = <Clock className="h-4 w-4 mr-1" />;
      break;
    case "processing":
      variant = "secondary";
      icon = <Package className="h-4 w-4 mr-1" />;
      break;
    case "shipped":
      variant = "default";
      icon = <Truck className="h-4 w-4 mr-1" />;
      break;
    case "delivered":
      variant = "default";
      icon = <CheckCircle className="h-4 w-4 mr-1" />;
      break;
    case "cancelled":
      variant = "destructive";
      icon = <XCircle className="h-4 w-4 mr-1" />;
      break;
  }

  return (
    <Badge variant={variant} className="flex items-center">
      {icon}
      <span className="capitalize">{status}</span>
    </Badge>
  );
};

export default function SellerOrdersPage() {
  const { t } = useTranslation("orders");
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("all");

  // Fetch orders
  const { data, isLoading, error } = useQuery({
    queryKey: ["sellerOrders", page, limit, statusFilter],
    queryFn: () => {
      return sellerOrdersService.getOrders({ 
        page, 
        limit, 
        status: statusFilter || undefined 
      });
    },
  });

  // Filter orders by search term
  const filteredOrders = data?.orders ? data.orders.filter(order => {
    // Kiểm tra order có hợp lệ không
    if (!order) {
      return false;
    }

    // Lấy ID từ _id hoặc id
    const orderId = order._id || order.id;
    if (!orderId) {
      return false;
    }

    const orderNumber = order.orderNumber || orderId;

    const matchesSearch = searchTerm
      ? (orderNumber.toString().includes(searchTerm) || 
         (order.user?.name && order.user.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
         (order.user?.email && order.user.email.toLowerCase().includes(searchTerm.toLowerCase())))
      : true;

    return matchesSearch;
  }) : [];

  // Handle pagination
  const totalPages = data?.totalPages || 1;

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  // Handle order click
  const handleOrderClick = (orderId: string) => {
    router.push(`/seller/orders/${orderId}`);
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    
    if (value === "all") {
      setStatusFilter(null);
    } else {
      setStatusFilter(value);
    }
    
    setPage(1); // Reset to first page when changing tabs
  };

  return (
    <div className="p-6">
      <div className="flex flex-col space-y-6">
        <div className="flex flex-col space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">{t("manageOrders") || "Quản lý đơn hàng"}</h2>
          <p className="text-muted-foreground">
            {t("manageOrdersDescription") || "Xem và quản lý tất cả đơn hàng của cửa hàng bạn"}
          </p>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("totalOrders") || "Tổng đơn hàng"}
              </CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data?.total || 0}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("pendingOrders") || "Đơn chờ xử lý"}
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {data?.orders?.filter(order => order.status === "pending").length || 0}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("processingOrders") || "Đơn đang xử lý"}
              </CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {data?.orders?.filter(order => order.status === "processing").length || 0}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("shippedOrders") || "Đơn đang giao"}
              </CardTitle>
              <Truck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {data?.orders?.filter(order => order.status === "shipped").length || 0}
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={handleTabChange}>
          <TabsList>
            <TabsTrigger value="all">{t("allOrders") || "Tất cả"}</TabsTrigger>
            <TabsTrigger value="pending">{t("pending") || "Chờ xử lý"}</TabsTrigger>
            <TabsTrigger value="processing">{t("processing") || "Đang xử lý"}</TabsTrigger>
            <TabsTrigger value="shipped">{t("shipped") || "Đang giao"}</TabsTrigger>
            <TabsTrigger value="delivered">{t("delivered") || "Đã giao"}</TabsTrigger>
            <TabsTrigger value="cancelled">{t("cancelled") || "Đã hủy"}</TabsTrigger>
          </TabsList>
          <TabsContent value={activeTab} className="mt-6">
            <div className="flex flex-col md:flex-row gap-4 mb-6 justify-between">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t("searchOrders") || "Tìm kiếm đơn hàng..."}
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  {t("show") || "Hiển thị"}:
                </span>
                <Select
                  value={limit.toString()}
                  onValueChange={(value) => setLimit(parseInt(value))}
                >
                  <SelectTrigger className="w-16">
                    <SelectValue placeholder="10" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-6 w-32" />
                      <Skeleton className="h-6 w-24" />
                    </div>
                    <div className="mt-4 space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-12 text-destructive">
                <p>{t("errorLoadingOrders") || "Lỗi tải đơn hàng"}</p>
                <p className="text-sm mt-2">{error instanceof Error ? error.message : String(error)}</p>
              </div>
            ) : filteredOrders.length === 0 ? (
              <div className="text-center py-12">
                <h3 className="text-xl font-medium">{t("noOrders") || "Không tìm thấy đơn hàng"}</h3>
                <p className="text-muted-foreground mt-2">
                  {searchTerm
                    ? t("noOrdersFiltered") || "Không có đơn hàng phù hợp với tìm kiếm của bạn"
                    : t("noOrdersYet") || "Chưa có đơn hàng nào"}
                </p>
                {searchTerm && (
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => setSearchTerm("")}
                  >
                    {t("clearFilters") || "Xóa bộ lọc"}
                  </Button>
                )}
              </div>
            ) : (
              <>
                <div className="border rounded-lg overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>{t("orderId") || "Mã đơn hàng"}</TableHead>
                        <TableHead>{t("customer") || "Khách hàng"}</TableHead>
                        <TableHead>{t("date") || "Ngày đặt"}</TableHead>
                        <TableHead>{t("status") || "Trạng thái"}</TableHead>
                        <TableHead className="text-right">{t("total") || "Tổng tiền"}</TableHead>
                        <TableHead></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredOrders.map((order) => {
                        if (!order) {
                          return null;
                        }

                        // Lấy ID từ _id hoặc id
                        const orderId = order._id || order.id;
                        if (!orderId) {
                          return null;
                        }

                        return (
                        <TableRow
                          key={orderId}
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => handleOrderClick(orderId.toString())}
                        >
                          <TableCell className="font-medium">
                            {order.orderNumber || `#${orderId.toString().slice(-6)}`}
                          </TableCell>
                          <TableCell>
                            {order.user?.name || 'N/A'}
                            <div className="text-xs text-muted-foreground">
                              {order.user?.email || 'N/A'}
                            </div>
                          </TableCell>
                          <TableCell>
                            {order.createdAt ? format(new Date(order.createdAt), "dd/MM/yyyy") : 'N/A'}
                          </TableCell>
                          <TableCell>
                            {order.status ? (
                              <OrderStatusBadge status={order.status} />
                            ) : (
                              <span>Unknown</span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' })
                              .format(order.totalAmount || order.total || 0)}
                          </TableCell>
                          <TableCell>
                            <Button variant="ghost" size="icon">
                              <ChevronRight className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>

                <div className="mt-6">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handlePageChange(page - 1)}
                          className={page <= 1 ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>

                      {Array.from({ length: totalPages }).map((_, i) => {
                        const pageNumber = i + 1;
                        return (
                        <PaginationItem key={`page-${pageNumber}`}>
                          <PaginationLink
                            isActive={page === pageNumber}
                            onClick={() => handlePageChange(pageNumber)}
                          >
                            {pageNumber}
                          </PaginationLink>
                        </PaginationItem>
                        );
                      })}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handlePageChange(page + 1)}
                          className={page >= totalPages ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              </>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
