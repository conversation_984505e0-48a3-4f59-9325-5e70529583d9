"use client"

import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@components/ui/tabs"
import { But<PERSON> } from "@components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { DatePicker } from "@components/ui/date-picker"
import { useAnalytics } from "@hooks/use-analytics"
import { FileDown, FileText, BarChart, Package, Calendar, Search } from "lucide-react"
import { format } from "date-fns"
import { vi } from "date-fns/locale"
import { toast } from "sonner"
import { Bar, Pie, Doughnut } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js"
import { Input } from "@components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@components/ui/table"

// Đăng ký các thành phần Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
)

export default function ProductsReportPage() {
  const { t } = useTranslation("reports")
  const [timeRange, setTimeRange] = useState<"day" | "week" | "month" | "year" | "custom">("month")
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)
  const [filters, setFilters] = useState<Record<string, any>>({})
  const [searchTerm, setSearchTerm] = useState("")
  
  const { generateReport, isGeneratingReport, generatedReport, exportReport, isExportingReport } = useAnalytics()
  
  // Khởi tạo báo cáo khi trang được tải
  useEffect(() => {
    handleGenerateReport()
  }, [])
  
  // Xử lý khi thay đổi khoảng thời gian
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value as "day" | "week" | "month" | "year" | "custom")
    
    // Reset ngày bắt đầu và kết thúc nếu không phải custom
    if (value !== "custom") {
      setStartDate(undefined)
      setEndDate(undefined)
      
      // Tự động tạo báo cáo khi thay đổi khoảng thời gian
      setTimeout(() => {
        handleGenerateReport()
      }, 100)
    } else {
      // Nếu là custom và chưa có ngày, thiết lập mặc định
      if (!startDate) {
        const today = new Date()
        const lastMonth = new Date()
        lastMonth.setMonth(today.getMonth() - 1)
        setStartDate(lastMonth)
        setEndDate(today)
      }
    }
  }
  
  // Xử lý khi thay đổi bộ lọc
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
    
    // Tự động tạo báo cáo khi thay đổi bộ lọc
    setTimeout(() => {
      handleGenerateReport()
    }, 100)
  }
  
  // Xử lý khi tạo báo cáo
  const handleGenerateReport = () => {
    // Kiểm tra nếu là custom thì phải có startDate và endDate
    if (timeRange === "custom" && (!startDate || !endDate)) {
      toast.error(t("pleaseSelectDateRange"))
      return
    }
    
    generateReport({
      type: "products",
      timeRange,
      startDate: startDate ? format(startDate, "yyyy-MM-dd") : undefined,
      endDate: endDate ? format(endDate, "yyyy-MM-dd") : undefined,
      filters
    })
  }
  
  // Xử lý khi xuất báo cáo
  const handleExportReport = (format: "csv" | "json" = "csv") => {
    // Kiểm tra nếu là custom thì phải có startDate và endDate
    if (timeRange === "custom" && (!startDate || !endDate)) {
      toast.error(t("pleaseSelectDateRange"))
      return
    }
    
    // Tạo tên file
    const fileName = `products_report_${format(new Date(), "yyyy-MM-dd")}.${format}`
    
    // Thông báo đang xuất báo cáo
    toast.info(t("exportingReport"))
    
    // Gọi API xuất báo cáo
    exportReport({
      type: "products",
      format,
      timeRange,
      startDate: startDate ? format(startDate, "yyyy-MM-dd") : undefined,
      endDate: endDate ? format(endDate, "yyyy-MM-dd") : undefined,
      filters
    })
    
    // Thông báo xuất báo cáo thành công
    toast.success(t("reportExportedSuccessfully", { fileName }))
  }
  
  // Format tiền tệ
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      maximumFractionDigits: 0
    }).format(value)
  }
  
  // Chuẩn bị dữ liệu cho biểu đồ top sản phẩm
  const prepareTopProductsChartData = () => {
    if (!generatedReport || !generatedReport.data || !generatedReport.data.topProducts) {
      return {
        labels: [],
        datasets: [
          {
            label: t("revenue"),
            data: [],
            backgroundColor: "rgba(53, 162, 235, 0.6)",
          }
        ]
      }
    }
    
    return {
      labels: generatedReport.data.topProducts.map((product: any) => product.name),
      datasets: [
        {
          label: t("revenue"),
          data: generatedReport.data.topProducts.map((product: any) => product.revenue),
          backgroundColor: "rgba(53, 162, 235, 0.6)",
        }
      ]
    }
  }
  
  // Chuẩn bị dữ liệu cho biểu đồ số lượng bán
  const prepareQuantityChartData = () => {
    if (!generatedReport || !generatedReport.data || !generatedReport.data.topProducts) {
      return {
        labels: [],
        datasets: [
          {
            label: t("quantity"),
            data: [],
            backgroundColor: "rgba(75, 192, 192, 0.6)",
          }
        ]
      }
    }
    
    return {
      labels: generatedReport.data.topProducts.map((product: any) => product.name),
      datasets: [
        {
          label: t("quantity"),
          data: generatedReport.data.topProducts.map((product: any) => product.quantity),
          backgroundColor: "rgba(75, 192, 192, 0.6)",
        }
      ]
    }
  }
  
  // Lọc danh sách sản phẩm theo từ khóa tìm kiếm
  const filteredProducts = () => {
    if (!generatedReport || !generatedReport.data || !generatedReport.data.productSales) {
      return []
    }
    
    return generatedReport.data.productSales.filter((product: any) => 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.category.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }
  
  return (
    <div className="container py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-3xl font-bold">{t("productsReport")}</h1>
        
        <div className="flex flex-col sm:flex-row gap-2 mt-4 md:mt-0">
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-[180px]">
              <Calendar className="mr-2 h-4 w-4" />
              <SelectValue placeholder={t("selectTimeRange")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">{t("today")}</SelectItem>
              <SelectItem value="week">{t("thisWeek")}</SelectItem>
              <SelectItem value="month">{t("thisMonth")}</SelectItem>
              <SelectItem value="year">{t("thisYear")}</SelectItem>
              <SelectItem value="custom">{t("customRange")}</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={() => handleExportReport("csv")}>
            <FileDown className="mr-2 h-4 w-4" />
            {t("exportCSV")}
          </Button>
        </div>
      </div>
      
      {timeRange === "custom" && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <div className="col-span-1">
                <label className="text-sm font-medium mb-1 block">{t("startDate")}</label>
                <DatePicker
                  selected={startDate}
                  onSelect={setStartDate}
                  maxDate={endDate || new Date()}
                />
              </div>
              <div className="col-span-1">
                <label className="text-sm font-medium mb-1 block">{t("endDate")}</label>
                <DatePicker
                  selected={endDate}
                  onSelect={setEndDate}
                  minDate={startDate}
                  maxDate={new Date()}
                />
              </div>
              <div className="col-span-1 sm:col-span-2 flex items-end">
                <Button onClick={handleGenerateReport} disabled={isGeneratingReport} className="mb-1 w-full sm:w-auto">
                  {isGeneratingReport ? t("generating") : t("generateReport")}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      {isGeneratingReport ? (
        <div className="flex items-center justify-center h-64">
          <p>{t("generatingReport")}</p>
        </div>
      ) : !generatedReport ? (
        <div className="flex flex-col items-center justify-center h-64 text-center">
          <FileText className="h-16 w-16 text-muted-foreground mb-4" />
          <p className="text-lg font-medium">{t("noReportGenerated")}</p>
          <p className="text-sm text-muted-foreground mt-1">{t("selectOptionsAndGenerate")}</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col">
                  <p className="text-sm text-muted-foreground">{t("totalProducts")}</p>
                  <p className="text-3xl font-bold">{generatedReport.data.totalProducts}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col">
                  <p className="text-sm text-muted-foreground">{t("totalQuantitySold")}</p>
                  <p className="text-3xl font-bold">{generatedReport.data.totalQuantity}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col">
                  <p className="text-sm text-muted-foreground">{t("totalRevenue")}</p>
                  <p className="text-3xl font-bold">{formatCurrency(generatedReport.data.totalRevenue)}</p>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card>
              <CardHeader>
                <CardTitle>{t("topProductsByRevenue")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <Bar 
                    data={prepareTopProductsChartData()} 
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      indexAxis: 'y' as const,
                      scales: {
                        x: {
                          beginAtZero: true,
                          ticks: {
                            callback: function(value) {
                              return new Intl.NumberFormat("vi-VN", {
                                style: "currency",
                                currency: "VND",
                                notation: "compact",
                                compactDisplay: "short",
                                maximumFractionDigits: 0,
                              }).format(value as number)
                            }
                          }
                        }
                      },
                      plugins: {
                        tooltip: {
                          callbacks: {
                            label: function(context) {
                              return `${context.dataset.label}: ${new Intl.NumberFormat("vi-VN", {
                                style: "currency",
                                currency: "VND",
                                maximumFractionDigits: 0,
                              }).format(context.parsed.x)}`
                            }
                          }
                        }
                      }
                    }}
                  />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t("topProductsByQuantity")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <Bar 
                    data={prepareQuantityChartData()} 
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      indexAxis: 'y' as const,
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>{t("productSales")}</CardTitle>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder={t("searchProducts")}
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("product")}</TableHead>
                      <TableHead>{t("sku")}</TableHead>
                      <TableHead>{t("category")}</TableHead>
                      <TableHead className="text-right">{t("price")}</TableHead>
                      <TableHead className="text-right">{t("quantity")}</TableHead>
                      <TableHead className="text-right">{t("revenue")}</TableHead>
                      <TableHead className="text-right">{t("orders")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts().map((product: any) => (
                      <TableRow key={product._id}>
                        <TableCell className="font-medium">{product.name}</TableCell>
                        <TableCell>{product.sku}</TableCell>
                        <TableCell>{product.category}</TableCell>
                        <TableCell className="text-right">{formatCurrency(product.price)}</TableCell>
                        <TableCell className="text-right">{product.quantity}</TableCell>
                        <TableCell className="text-right">{formatCurrency(product.revenue)}</TableCell>
                        <TableCell className="text-right">{product.orders}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
