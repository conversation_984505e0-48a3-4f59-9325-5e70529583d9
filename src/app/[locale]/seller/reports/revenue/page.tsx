"use client"

import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@components/ui/tabs"
import { Button } from "@components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { DatePicker } from "@components/ui/date-picker"
import { useAnalytics } from "@hooks/use-analytics"
import { FileDown, FileText, BarChart, DollarSign, Calendar, ArrowDown, ArrowUp } from "lucide-react"
import { format, subDays, subMonths } from "date-fns"
import { vi } from "date-fns/locale"
import { toast } from "sonner"
import { Line, Bar, Pie } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  <PERSON>,
  Filler,
} from "chart.js"

// Đ<PERSON>ng ký các thành phần Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

export default function RevenueReportPage() {
  const { t } = useTranslation("reports")
  const [timeRange, setTimeRange] = useState<"day" | "week" | "month" | "year" | "custom">("month")
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)
  const [filters, setFilters] = useState<Record<string, any>>({})
  const [activeTab, setActiveTab] = useState("overview")
  
  const { generateReport, isGeneratingReport, generatedReport, exportReport, isExportingReport } = useAnalytics()
  
  // Khởi tạo báo cáo khi trang được tải
  useEffect(() => {
    handleGenerateReport()
  }, [])
  
  // Xử lý khi thay đổi khoảng thời gian
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value as "day" | "week" | "month" | "year" | "custom")
    
    // Reset ngày bắt đầu và kết thúc nếu không phải custom
    if (value !== "custom") {
      setStartDate(undefined)
      setEndDate(undefined)
      
      // Tự động tạo báo cáo khi thay đổi khoảng thời gian
      setTimeout(() => {
        handleGenerateReport()
      }, 100)
    } else {
      // Nếu là custom và chưa có ngày, thiết lập mặc định
      if (!startDate) {
        const today = new Date()
        const lastMonth = new Date()
        lastMonth.setMonth(today.getMonth() - 1)
        setStartDate(lastMonth)
        setEndDate(today)
      }
    }
  }
  
  // Xử lý khi thay đổi bộ lọc
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
    
    // Tự động tạo báo cáo khi thay đổi bộ lọc
    setTimeout(() => {
      handleGenerateReport()
    }, 100)
  }
  
  // Xử lý khi tạo báo cáo
  const handleGenerateReport = () => {
    // Kiểm tra nếu là custom thì phải có startDate và endDate
    if (timeRange === "custom" && (!startDate || !endDate)) {
      toast.error(t("pleaseSelectDateRange"))
      return
    }
    
    generateReport({
      type: "revenue",
      timeRange,
      startDate: startDate ? format(startDate, "yyyy-MM-dd") : undefined,
      endDate: endDate ? format(endDate, "yyyy-MM-dd") : undefined,
      filters
    })
  }
  
  // Xử lý khi xuất báo cáo
  const handleExportReport = (format: "csv" | "json" = "csv") => {
    // Kiểm tra nếu là custom thì phải có startDate và endDate
    if (timeRange === "custom" && (!startDate || !endDate)) {
      toast.error(t("pleaseSelectDateRange"))
      return
    }
    
    // Tạo tên file
    const fileName = `revenue_report_${format(new Date(), "yyyy-MM-dd")}.${format}`
    
    // Thông báo đang xuất báo cáo
    toast.info(t("exportingReport"))
    
    // Gọi API xuất báo cáo
    exportReport({
      type: "revenue",
      format,
      timeRange,
      startDate: startDate ? format(startDate, "yyyy-MM-dd") : undefined,
      endDate: endDate ? format(endDate, "yyyy-MM-dd") : undefined,
      filters
    })
    
    // Thông báo xuất báo cáo thành công
    toast.success(t("reportExportedSuccessfully", { fileName }))
  }
  
  // Format tiền tệ
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      maximumFractionDigits: 0
    }).format(value)
  }
  
  // Format phần trăm
  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }
  
  // Chuẩn bị dữ liệu cho biểu đồ doanh thu theo ngày
  const prepareRevenueChartData = () => {
    if (!generatedReport || !generatedReport.data || !generatedReport.data.dailyRevenue) {
      return {
        labels: [],
        datasets: [
          {
            label: t("revenue"),
            data: [],
            borderColor: "rgb(53, 162, 235)",
            backgroundColor: "rgba(53, 162, 235, 0.5)",
          }
        ]
      }
    }
    
    return {
      labels: generatedReport.data.dailyRevenue.map((day: any) => format(new Date(day.date), 'dd/MM', { locale: vi })),
      datasets: [
        {
          label: t("revenue"),
          data: generatedReport.data.dailyRevenue.map((day: any) => day.revenue),
          borderColor: "rgb(53, 162, 235)",
          backgroundColor: "rgba(53, 162, 235, 0.5)",
          tension: 0.3,
          fill: true,
        }
      ]
    }
  }
  
  // Chuẩn bị dữ liệu cho biểu đồ đơn hàng theo ngày
  const prepareOrdersChartData = () => {
    if (!generatedReport || !generatedReport.data || !generatedReport.data.dailyRevenue) {
      return {
        labels: [],
        datasets: [
          {
            label: t("orders"),
            data: [],
            backgroundColor: "rgba(75, 192, 192, 0.6)",
          }
        ]
      }
    }
    
    return {
      labels: generatedReport.data.dailyRevenue.map((day: any) => format(new Date(day.date), 'dd/MM', { locale: vi })),
      datasets: [
        {
          label: t("orders"),
          data: generatedReport.data.dailyRevenue.map((day: any) => day.orders),
          backgroundColor: "rgba(75, 192, 192, 0.6)",
        }
      ]
    }
  }
  
  // Chuẩn bị dữ liệu cho biểu đồ trạng thái đơn hàng
  const prepareOrderStatusChartData = () => {
    if (!generatedReport || !generatedReport.data || !generatedReport.data.ordersByStatus) {
      return {
        labels: [],
        datasets: [
          {
            data: [],
            backgroundColor: [],
          }
        ]
      }
    }
    
    const statusColors = {
      pending: "rgba(255, 206, 86, 0.6)",
      processing: "rgba(54, 162, 235, 0.6)",
      shipped: "rgba(153, 102, 255, 0.6)",
      delivered: "rgba(75, 192, 192, 0.6)",
      cancelled: "rgba(255, 99, 132, 0.6)",
    }
    
    const statuses = Object.keys(generatedReport.data.ordersByStatus)
    
    return {
      labels: statuses.map(status => t(status)),
      datasets: [
        {
          data: statuses.map(status => generatedReport.data.ordersByStatus[status]),
          backgroundColor: statuses.map(status => statusColors[status as keyof typeof statusColors] || "rgba(201, 203, 207, 0.6)"),
        }
      ]
    }
  }
  
  // Chuẩn bị dữ liệu cho biểu đồ phương thức thanh toán
  const preparePaymentMethodChartData = () => {
    if (!generatedReport || !generatedReport.data || !generatedReport.data.paymentMethods) {
      return {
        labels: [],
        datasets: [
          {
            data: [],
            backgroundColor: [],
          }
        ]
      }
    }
    
    const methodColors = {
      credit_card: "rgba(255, 99, 132, 0.6)",
      bank_transfer: "rgba(54, 162, 235, 0.6)",
      cash: "rgba(75, 192, 192, 0.6)",
      paypal: "rgba(153, 102, 255, 0.6)",
    }
    
    const methods = Object.keys(generatedReport.data.paymentMethods)
    
    return {
      labels: methods.map(method => t(method)),
      datasets: [
        {
          data: methods.map(method => generatedReport.data.paymentMethods[method]),
          backgroundColor: methods.map(method => methodColors[method as keyof typeof methodColors] || "rgba(201, 203, 207, 0.6)"),
        }
      ]
    }
  }
  
  return (
    <div className="container py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-3xl font-bold">{t("revenueReport")}</h1>
        
        <div className="flex flex-col sm:flex-row gap-2 mt-4 md:mt-0">
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-[180px]">
              <Calendar className="mr-2 h-4 w-4" />
              <SelectValue placeholder={t("selectTimeRange")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">{t("today")}</SelectItem>
              <SelectItem value="week">{t("thisWeek")}</SelectItem>
              <SelectItem value="month">{t("thisMonth")}</SelectItem>
              <SelectItem value="year">{t("thisYear")}</SelectItem>
              <SelectItem value="custom">{t("customRange")}</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={() => handleExportReport("csv")}>
            <FileDown className="mr-2 h-4 w-4" />
            {t("exportCSV")}
          </Button>
        </div>
      </div>
      
      {timeRange === "custom" && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <div className="col-span-1">
                <label className="text-sm font-medium mb-1 block">{t("startDate")}</label>
                <DatePicker
                  selected={startDate}
                  onSelect={setStartDate}
                  maxDate={endDate || new Date()}
                />
              </div>
              <div className="col-span-1">
                <label className="text-sm font-medium mb-1 block">{t("endDate")}</label>
                <DatePicker
                  selected={endDate}
                  onSelect={setEndDate}
                  minDate={startDate}
                  maxDate={new Date()}
                />
              </div>
              <div className="col-span-1 sm:col-span-2 flex items-end">
                <Button onClick={handleGenerateReport} disabled={isGeneratingReport} className="mb-1 w-full sm:w-auto">
                  {isGeneratingReport ? t("generating") : t("generateReport")}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      {isGeneratingReport ? (
        <div className="flex items-center justify-center h-64">
          <p>{t("generatingReport")}</p>
        </div>
      ) : !generatedReport ? (
        <div className="flex flex-col items-center justify-center h-64 text-center">
          <FileText className="h-16 w-16 text-muted-foreground mb-4" />
          <p className="text-lg font-medium">{t("noReportGenerated")}</p>
          <p className="text-sm text-muted-foreground mt-1">{t("selectOptionsAndGenerate")}</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-muted-foreground">{t("totalRevenue")}</p>
                    <p className="text-3xl font-bold">{formatCurrency(generatedReport.data.totalRevenue)}</p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${generatedReport.data.revenueChange >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {generatedReport.data.revenueChange >= 0 ? (
                      <ArrowUp className="inline h-3 w-3 mr-1" />
                    ) : (
                      <ArrowDown className="inline h-3 w-3 mr-1" />
                    )}
                    {formatPercentage(generatedReport.data.revenueChange)}
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-muted-foreground">{t("totalOrders")}</p>
                    <p className="text-3xl font-bold">{generatedReport.data.totalOrders}</p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${generatedReport.data.ordersChange >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {generatedReport.data.ordersChange >= 0 ? (
                      <ArrowUp className="inline h-3 w-3 mr-1" />
                    ) : (
                      <ArrowDown className="inline h-3 w-3 mr-1" />
                    )}
                    {formatPercentage(generatedReport.data.ordersChange)}
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm text-muted-foreground">{t("averageOrderValue")}</p>
                    <p className="text-3xl font-bold">{formatCurrency(generatedReport.data.averageOrderValue)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Tabs defaultValue="revenue" className="mb-6">
            <TabsList>
              <TabsTrigger value="revenue">{t("revenueOverTime")}</TabsTrigger>
              <TabsTrigger value="orders">{t("ordersOverTime")}</TabsTrigger>
            </TabsList>
            <TabsContent value="revenue" className="pt-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="h-80">
                    <Line 
                      data={prepareRevenueChartData()} 
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: {
                            beginAtZero: true,
                            ticks: {
                              callback: function(value) {
                                return new Intl.NumberFormat("vi-VN", {
                                  style: "currency",
                                  currency: "VND",
                                  notation: "compact",
                                  compactDisplay: "short",
                                  maximumFractionDigits: 0,
                                }).format(value as number)
                              }
                            }
                          }
                        },
                        plugins: {
                          tooltip: {
                            callbacks: {
                              label: function(context) {
                                return `${context.dataset.label}: ${new Intl.NumberFormat("vi-VN", {
                                  style: "currency",
                                  currency: "VND",
                                  maximumFractionDigits: 0,
                                }).format(context.parsed.y)}`
                              }
                            }
                          }
                        }
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="orders" className="pt-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="h-80">
                    <Bar 
                      data={prepareOrdersChartData()} 
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card>
              <CardHeader>
                <CardTitle>{t("ordersByStatus")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <Pie 
                    data={prepareOrderStatusChartData()} 
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                    }}
                  />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t("paymentMethods")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <Pie 
                    data={preparePaymentMethodChartData()} 
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  )
}
