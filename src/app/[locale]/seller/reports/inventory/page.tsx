"use client"

import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@components/ui/card"
import { But<PERSON> } from "@components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { useAnalytics } from "@hooks/use-analytics"
import { FileDown, FileText, Package, Search, AlertTriangle } from "lucide-react"
import { toast } from "sonner"
import { Pie, Bar } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js"
import { Input } from "@components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@components/ui/table"
import { Badge } from "@components/ui/badge"

// <PERSON><PERSON>ng ký các thành phần Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
)

export default function InventoryReportPage() {
  const { t } = useTranslation("reports")
  const [filters, setFilters] = useState<Record<string, any>>({})
  const [searchTerm, setSearchTerm] = useState("")
  
  const { generateReport, isGeneratingReport, generatedReport, exportReport, isExportingReport } = useAnalytics()
  
  // Khởi tạo báo cáo khi trang được tải
  useEffect(() => {
    handleGenerateReport()
  }, [])
  
  // Xử lý khi thay đổi bộ lọc
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
    
    // Tự động tạo báo cáo khi thay đổi bộ lọc
    setTimeout(() => {
      handleGenerateReport()
    }, 100)
  }
  
  // Xử lý khi tạo báo cáo
  const handleGenerateReport = () => {
    generateReport({
      type: "inventory",
      timeRange: "day", // Không quan trọng với báo cáo tồn kho
      filters
    })
  }
  
  // Xử lý khi xuất báo cáo
  const handleExportReport = (format: "csv" | "json" = "csv") => {
    // Tạo tên file
    const fileName = `inventory_report_${new Date().toISOString().split('T')[0]}.${format}`
    
    // Thông báo đang xuất báo cáo
    toast.info(t("exportingReport"))
    
    // Gọi API xuất báo cáo
    exportReport({
      type: "inventory",
      format,
      timeRange: "day", // Không quan trọng với báo cáo tồn kho
      filters
    })
    
    // Thông báo xuất báo cáo thành công
    toast.success(t("reportExportedSuccessfully", { fileName }))
  }
  
  // Format tiền tệ
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      maximumFractionDigits: 0
    }).format(value)
  }
  
  // Chuẩn bị dữ liệu cho biểu đồ danh mục
  const prepareCategoryChartData = () => {
    if (!generatedReport || !generatedReport.data || !generatedReport.data.categorySummary) {
      return {
        labels: [],
        datasets: [
          {
            data: [],
            backgroundColor: [],
          }
        ]
      }
    }
    
    const colors = [
      "rgba(255, 99, 132, 0.6)",
      "rgba(54, 162, 235, 0.6)",
      "rgba(255, 206, 86, 0.6)",
      "rgba(75, 192, 192, 0.6)",
      "rgba(153, 102, 255, 0.6)",
      "rgba(255, 159, 64, 0.6)",
      "rgba(201, 203, 207, 0.6)",
    ]
    
    return {
      labels: generatedReport.data.categorySummary.map((category: any) => category.category),
      datasets: [
        {
          data: generatedReport.data.categorySummary.map((category: any) => category.totalValue),
          backgroundColor: generatedReport.data.categorySummary.map((_: any, index: number) => colors[index % colors.length]),
        }
      ]
    }
  }
  
  // Chuẩn bị dữ liệu cho biểu đồ số lượng sản phẩm theo danh mục
  const prepareCategoryProductCountChartData = () => {
    if (!generatedReport || !generatedReport.data || !generatedReport.data.categorySummary) {
      return {
        labels: [],
        datasets: [
          {
            label: t("productCount"),
            data: [],
            backgroundColor: "rgba(75, 192, 192, 0.6)",
          }
        ]
      }
    }
    
    return {
      labels: generatedReport.data.categorySummary.map((category: any) => category.category),
      datasets: [
        {
          label: t("productCount"),
          data: generatedReport.data.categorySummary.map((category: any) => category.productCount),
          backgroundColor: "rgba(75, 192, 192, 0.6)",
        }
      ]
    }
  }
  
  // Lọc danh sách sản phẩm theo từ khóa tìm kiếm
  const filteredProducts = () => {
    if (!generatedReport || !generatedReport.data || !generatedReport.data.products) {
      return []
    }
    
    return generatedReport.data.products.filter((product: any) => 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.category.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }
  
  // Lọc danh sách sản phẩm có tồn kho thấp
  const lowStockProducts = () => {
    if (!generatedReport || !generatedReport.data || !generatedReport.data.lowStockProducts) {
      return []
    }
    
    return generatedReport.data.lowStockProducts.filter((product: any) => 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.category.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }
  
  return (
    <div className="container py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-3xl font-bold">{t("inventoryReport")}</h1>
        
        <div className="flex flex-col sm:flex-row gap-2 mt-4 md:mt-0">
          <Select
            value={filters.category || "all"}
            onValueChange={(value) => handleFilterChange("category", value === "all" ? undefined : value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t("allCategories")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("allCategories")}</SelectItem>
              <SelectItem value="electronics">{t("electronics")}</SelectItem>
              <SelectItem value="clothing">{t("clothing")}</SelectItem>
              <SelectItem value="home">{t("home")}</SelectItem>
              <SelectItem value="beauty">{t("beauty")}</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={() => handleExportReport("csv")}>
            <FileDown className="mr-2 h-4 w-4" />
            {t("exportCSV")}
          </Button>
        </div>
      </div>
      
      {isGeneratingReport ? (
        <div className="flex items-center justify-center h-64">
          <p>{t("generatingReport")}</p>
        </div>
      ) : !generatedReport ? (
        <div className="flex flex-col items-center justify-center h-64 text-center">
          <FileText className="h-16 w-16 text-muted-foreground mb-4" />
          <p className="text-lg font-medium">{t("noReportGenerated")}</p>
          <p className="text-sm text-muted-foreground mt-1">{t("selectOptionsAndGenerate")}</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col">
                  <p className="text-sm text-muted-foreground">{t("totalProducts")}</p>
                  <p className="text-3xl font-bold">{generatedReport.data.totalProducts}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col">
                  <p className="text-sm text-muted-foreground">{t("totalStock")}</p>
                  <p className="text-3xl font-bold">{generatedReport.data.totalStock}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col">
                  <p className="text-sm text-muted-foreground">{t("totalStockValue")}</p>
                  <p className="text-3xl font-bold">{formatCurrency(generatedReport.data.totalStockValue)}</p>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card>
              <CardHeader>
                <CardTitle>{t("stockValueByCategory")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <Pie 
                    data={prepareCategoryChartData()} 
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        tooltip: {
                          callbacks: {
                            label: function(context) {
                              return `${context.label}: ${new Intl.NumberFormat("vi-VN", {
                                style: "currency",
                                currency: "VND",
                                maximumFractionDigits: 0,
                              }).format(context.raw as number)}`
                            }
                          }
                        }
                      }
                    }}
                  />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t("productCountByCategory")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <Bar 
                    data={prepareCategoryProductCountChartData()} 
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card className="mb-6">
            <CardHeader>
              <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-amber-500" />
                  <CardTitle>{t("lowStockProducts")}</CardTitle>
                </div>
                <div className="relative w-full md:w-64">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder={t("searchProducts")}
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("product")}</TableHead>
                      <TableHead>{t("sku")}</TableHead>
                      <TableHead>{t("category")}</TableHead>
                      <TableHead className="text-right">{t("stock")}</TableHead>
                      <TableHead className="text-right">{t("minStockThreshold")}</TableHead>
                      <TableHead className="text-right">{t("price")}</TableHead>
                      <TableHead className="text-right">{t("value")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {lowStockProducts().map((product: any) => (
                      <TableRow key={product._id}>
                        <TableCell className="font-medium">{product.name}</TableCell>
                        <TableCell>{product.sku}</TableCell>
                        <TableCell>{product.category}</TableCell>
                        <TableCell className="text-right">
                          <Badge variant={product.stock === 0 ? "destructive" : "warning"}>
                            {product.stock}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">{product.minStockThreshold}</TableCell>
                        <TableCell className="text-right">{formatCurrency(product.price)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(product.value)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
                <CardTitle>{t("allProducts")}</CardTitle>
                <div className="relative w-full md:w-64">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder={t("searchProducts")}
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("product")}</TableHead>
                      <TableHead>{t("sku")}</TableHead>
                      <TableHead>{t("category")}</TableHead>
                      <TableHead className="text-right">{t("stock")}</TableHead>
                      <TableHead className="text-right">{t("minStockThreshold")}</TableHead>
                      <TableHead className="text-right">{t("price")}</TableHead>
                      <TableHead className="text-right">{t("value")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts().map((product: any) => (
                      <TableRow key={product._id}>
                        <TableCell className="font-medium">{product.name}</TableCell>
                        <TableCell>{product.sku}</TableCell>
                        <TableCell>{product.category}</TableCell>
                        <TableCell className="text-right">
                          <Badge variant={
                            product.stock === 0 ? "destructive" : 
                            product.stock <= product.minStockThreshold ? "warning" : 
                            "secondary"
                          }>
                            {product.stock}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">{product.minStockThreshold}</TableCell>
                        <TableCell className="text-right">{formatCurrency(product.price)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(product.value)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
