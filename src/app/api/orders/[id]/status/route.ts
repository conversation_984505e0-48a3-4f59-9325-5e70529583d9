import { NextResponse } from "next/server";
import { z } from "zod";
import dbConnect from "@/lib/mongodb";
import { ensureModelsRegistered, Order, Shop } from "@/lib/models";
import { getAuthUser } from "@/lib/auth-utils";
import { sendOrderStatusUpdateEmail } from "@/lib/email-utils";

// Schema cho cập nhật trạng thái đơn hàng
const updateOrderStatusSchema = z.object({
  status: z.enum(["pending", "processing", "shipped", "delivered", "cancelled"]),
  note: z.string().optional(),
});

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect();

    // Đảm bảo tất cả các models được đăng ký
    ensureModelsRegistered();

    const user = await getAuthUser(request);

    if (!user) {
      return NextResponse.json(
        { error: "<PERSON>h<PERSON><PERSON> có quyền truy cập" },
        { status: 401 }
      );
    }

    // Chỉ admin hoặc seller mới có thể cập nhật trạng thái đơn hàng
    if (user.role !== "admin" && user.role !== "seller") {
      return NextResponse.json(
        { error: "Không có quyền cập nhật trạng thái đơn hàng" },
        { status: 403 }
      );
    }

    const orderId = params.id;
    const body = await request.json();

    // Validate dữ liệu đầu vào
    const validatedData = updateOrderStatusSchema.parse(body);

    // Tìm đơn hàng
    const order = await Order.findById(orderId)
      .populate({
        path: "items.product",
        select: "shop",
      });

    if (!order) {
      return NextResponse.json(
        { error: "Đơn hàng không tồn tại" },
        { status: 404 }
      );
    }

    // Kiểm tra quyền cập nhật
    // - Admin có thể cập nhật tất cả đơn hàng
    // - Seller chỉ có thể cập nhật đơn hàng có sản phẩm của shop mình
    if (user.role === "seller") {
      let hasPermission = false;

      // Kiểm tra xem seller có sản phẩm trong đơn hàng không
      for (const item of order.items) {
        if (item.product && item.product.shop) {
          const shopId = item.product.shop._id || item.product.shop;
          const shop = await Shop.findOne({ _id: shopId, owner: user._id });
          if (shop) {
            hasPermission = true;
            break;
          }
        }
      }

      if (!hasPermission) {
        return NextResponse.json(
          { error: "Không có quyền cập nhật đơn hàng này" },
          { status: 403 }
        );
      }
    }

    // Lưu trạng thái cũ để gửi email
    const previousStatus = order.status;

    // Cập nhật trạng thái đơn hàng
    order.status = validatedData.status;

    // Thêm vào lịch sử trạng thái
    order.statusHistory.push({
      status: validatedData.status,
      timestamp: new Date(),
      note: validatedData.note || `Trạng thái đơn hàng thay đổi thành ${validatedData.status}`,
    });

    await order.save();

    // Gửi email thông báo cập nhật trạng thái đơn hàng
    try {
      // Lấy thông tin đầy đủ của đơn hàng để gửi email
      const orderWithDetails = await Order.findById(order._id)
        .populate("user", "name email")
        .populate("items.product", "title price images");

      if (orderWithDetails) {
        await sendOrderStatusUpdateEmail(orderWithDetails, previousStatus);
      }
    } catch (emailError) {
      console.error("Error sending order status update email:", emailError);
      // Không trả về lỗi vì việc gửi email không ảnh hưởng đến việc cập nhật trạng thái
    }

    return NextResponse.json({
      message: "Cập nhật trạng thái đơn hàng thành công",
      order,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error updating order status:", error);
    return NextResponse.json(
      { error: "Lỗi cập nhật trạng thái đơn hàng" },
      { status: 500 }
    );
  }
}
