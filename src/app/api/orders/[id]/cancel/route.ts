import { NextResponse } from "next/server";
import { z } from "zod";
import dbConnect from "@/lib/mongodb";
import { ensureModelsRegistered, Order, Product } from "@/lib/models";
import { getAuthUser } from "@/lib/auth-utils";

// Schema cho hủy đơn hàng
const cancelOrderSchema = z.object({
  reason: z.string().optional(),
});

export async function POST(request: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect();

    // Đảm bảo tất cả các models được đăng ký
    ensureModelsRegistered();

    const user = await getAuthUser(request);

    if (!user) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const orderId = params.id;
    const body = await request.json();
    
    // Validate dữ liệu đầu vào
    const validatedData = cancelOrderSchema.parse(body);

    // Tìm đơn hàng
    const order = await Order.findById(orderId);

    if (!order) {
      return NextResponse.json(
        { error: "Đơn hàng không tồn tại" },
        { status: 404 }
      );
    }

    // Kiểm tra quyền hủy đơn hàng
    // - Người dùng chỉ có thể hủy đơn hàng của mình
    // - Admin có thể hủy tất cả đơn hàng
    if (order.user.toString() !== user._id.toString() && user.role !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền hủy đơn hàng này" },
        { status: 403 }
      );
    }

    // Kiểm tra trạng thái đơn hàng
    // Chỉ có thể hủy đơn hàng ở trạng thái pending hoặc processing
    if (order.status !== "pending" && order.status !== "processing") {
      return NextResponse.json(
        { error: "Không thể hủy đơn hàng ở trạng thái này" },
        { status: 400 }
      );
    }

    // Cập nhật trạng thái đơn hàng thành cancelled
    order.status = "cancelled";
    
    // Thêm vào lịch sử trạng thái
    order.statusHistory.push({
      status: "cancelled",
      timestamp: new Date(),
      note: validatedData.reason || "Đơn hàng đã bị hủy bởi người dùng",
    });

    // Hoàn trả số lượng sản phẩm vào kho
    for (const item of order.items) {
      const product = await Product.findById(item.product);
      if (product) {
        product.stock += item.quantity;
        await product.save();
      }
    }

    await order.save();

    return NextResponse.json({
      message: "Hủy đơn hàng thành công",
      order,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error cancelling order:", error);
    return NextResponse.json(
      { error: "Lỗi hủy đơn hàng" },
      { status: 500 }
    );
  }
}
