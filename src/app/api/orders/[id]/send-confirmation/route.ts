import { NextResponse } from "next/server";
import dbConnect from "@/lib/mongodb";
import { ensureModelsRegistered, Order } from "@/lib/models";
import { getAuthUser } from "@/lib/auth-utils";
import { sendOrderConfirmationEmail } from "@/lib/email-utils";

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    // Đảm bảo tất cả các models được đăng ký
    ensureModelsRegistered();

    const user = await getAuthUser(request);

    if (!user) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const orderId = params.id;

    // Tìm đơn hàng
    const order = await Order.findById(orderId)
      .populate("user", "name email")
      .populate("items.product", "title price images");

    if (!order) {
      return NextResponse.json(
        { error: "Đơn hàng không tồn tại" },
        { status: 404 }
      );
    }

    // Kiểm tra quyền truy cập
    // - Người dùng tạo đơn hàng
    // - Admin
    // - Seller của shop có sản phẩm trong đơn hàng
    const isOrderOwner = order.user._id.toString() === user._id.toString();
    const isAdmin = user.role === "admin";
    
    if (!isOrderOwner && !isAdmin && user.role !== "seller") {
      return NextResponse.json(
        { error: "Không có quyền gửi email xác nhận đơn hàng này" },
        { status: 403 }
      );
    }

    // Gửi email xác nhận đơn hàng
    const emailSent = await sendOrderConfirmationEmail(order);

    if (!emailSent) {
      return NextResponse.json(
        { error: "Không thể gửi email xác nhận đơn hàng" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "Email xác nhận đơn hàng đã được gửi",
    });
  } catch (error) {
    console.error("Error sending order confirmation email:", error);
    return NextResponse.json(
      { error: "Lỗi gửi email xác nhận đơn hàng" },
      { status: 500 }
    );
  }
}
