import { NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Order from '@/models/Order';
import { getAuthUser } from '@/lib/auth-utils';

export async function GET(request: Request) {
  try {
    await dbConnect();

    // <PERSON><PERSON><PERSON> thực người dùng
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Không có quyền truy cập' },
        { status: 401 }
      );
    }

    // Lấy tham số từ URL
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const status = url.searchParams.get('status');
    const sortBy = url.searchParams.get('sortBy') || 'createdAt';
    const sortOrder = url.searchParams.get('sortOrder') || 'desc';

    // <PERSON><PERSON><PERSON> toán skip cho phân trang
    const skip = (page - 1) * limit;

    // Tạo query filter
    const filter: any = { user: user._id };
    if (status) {
      filter.status = status;
    }

    // Tạo sort options
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Đếm tổng số đơn hàng
    const total = await Order.countDocuments(filter);

    // Lấy danh sách đơn hàng
    const orders = await Order.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('items.product', 'name price images')
      .populate('shippingAddress', 'fullName phoneNumber addressLine1 city district ward');

    // Tính toán tổng số trang
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      orders,
      pagination: {
        total,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching user order history:', error);
    return NextResponse.json(
      { error: 'Lỗi máy chủ nội bộ' },
      { status: 500 }
    );
  }
}
