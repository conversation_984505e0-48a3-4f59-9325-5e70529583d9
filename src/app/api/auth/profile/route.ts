import { NextResponse } from 'next/server';
import { getAuthUser } from '@/lib/auth-utils';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import Order from '@/models/Order';
import ShippingAddress from '@/models/ShippingAddress';
import { z } from 'zod';

export async function GET(request: Request) {
  try {
    await dbConnect();

    const user = await getAuthUser(request);

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // L<PERSON>y thêm thông tin bổ sung
    const [orderCount, defaultAddress] = await Promise.all([
      Order.countDocuments({ user: user._id }),
      ShippingAddress.findOne({ user: user._id, isDefault: true }).lean()
    ]);

    return NextResponse.json({
      id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      avatar: user.avatar,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      stats: {
        orderCount,
        hasDefaultAddress: !!defaultAddress
      }
    });
  } catch (error) {
    console.error('Error fetching profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

const updateProfileSchema = z.object({
  name: z.string().min(2, 'Tên phải có ít nhất 2 ký tự').optional(),
  avatar: z.string().optional(),
  phone: z.string().optional(),
  bio: z.string().optional(),
  preferences: z.object({
    newsletter: z.boolean().optional(),
    notifications: z.boolean().optional(),
    language: z.string().optional(),
  }).optional(),
});

export async function PUT(request: Request) {
  try {
    await dbConnect();

    const user = await getAuthUser(request);

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updateProfileSchema.parse(body);

    // Cập nhật thông tin người dùng
    const updatedUser = await User.findByIdAndUpdate(
      user._id,
      { $set: validatedData },
      { new: true }
    ).select('-password');

    if (!updatedUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Lấy thêm thông tin bổ sung
    const [orderCount, defaultAddress] = await Promise.all([
      Order.countDocuments({ user: user._id }),
      ShippingAddress.findOne({ user: user._id, isDefault: true }).lean()
    ]);

    return NextResponse.json({
      id: updatedUser._id,
      name: updatedUser.name,
      email: updatedUser.email,
      role: updatedUser.role,
      avatar: updatedUser.avatar,
      phone: updatedUser.phone,
      bio: updatedUser.bio,
      preferences: updatedUser.preferences,
      createdAt: updatedUser.createdAt,
      updatedAt: updatedUser.updatedAt,
      stats: {
        orderCount,
        hasDefaultAddress: !!defaultAddress
      }
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error('Error updating profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
