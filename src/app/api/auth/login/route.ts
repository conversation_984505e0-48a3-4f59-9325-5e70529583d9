import { NextResponse } from 'next/server';
import { z } from 'zod';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import { signToken } from '@/lib/jwt';

const loginSchema = z.object({
  email: z.string().email('Email không hợp lệ'),
  password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
  rememberMe: z.boolean().optional().default(false),
});

export async function POST(request: Request) {
  try {
    await dbConnect();

    const body = await request.json();
    const validatedData = loginSchema.parse(body);

    // Tìm user theo email
    const user = await User.findOne({ email: validatedData.email });

    if (!user) {
      return NextResponse.json(
        { error: 'Email hoặc mật khẩu không chính xác' },
        { status: 401 }
      );
    }

    // <PERSON><PERSON><PERSON> tra mật khẩu
    const isPasswordValid = await user.comparePassword(validatedData.password);

    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'Email hoặc mật khẩu không chính xác' },
        { status: 401 }
      );
    }

    // Tạo JWT token
    const token = signToken({
      id: user._id,
      email: user.email,
      role: user.role,
    });

    // Tạo response với cookie
    const response = NextResponse.json({
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        avatar: user.avatar,
      },
      token,
    });

    // Tính thời gian hết hạn cookie dựa trên tùy chọn "Nhớ mật khẩu"
    const maxAge = validatedData.rememberMe
      ? 30 * 24 * 60 * 60 // 30 days if remember me is checked
      : 1 * 24 * 60 * 60;  // 1 day if not checked

    // Thêm cookie
    response.cookies.set({
      name: 'token',
      value: token,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: maxAge, // Thời gian hết hạn tùy thuộc vào tùy chọn "Nhớ mật khẩu"
      path: '/',
    });

    return response;
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error('Error during login:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
