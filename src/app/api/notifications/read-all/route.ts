import { NextRequest, NextResponse } from "next/server"
import dbConnect from "@/lib/mongodb"
import { getAuthUser } from "@/lib/auth-utils"
import { ensureModelsRegistered } from "@/lib/models"
import Notification from "@/models/Notification"

// Ensure models are registered
ensureModelsRegistered()

export async function PUT(request: NextRequest) {
  try {
    await dbConnect()

    // Lấy thông tin người dùng từ token
    const user = await getAuthUser(request)

    if (!user) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      )
    }

    // Đ<PERSON>h dấu tất cả thông báo đã đọc
    const result = await Notification.updateMany(
      { user: user._id, read: false },
      { $set: { read: true } }
    )

    return NextResponse.json({
      success: true,
      modifiedCount: result.modifiedCount,
    })
  } catch (error) {
    console.error("Error marking all notifications as read:", error)
    return NextResponse.json(
      { error: "Lỗi đánh dấu tất cả thông báo đã đọc" },
      { status: 500 }
    )
  }
}
