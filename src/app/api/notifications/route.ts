import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import dbConnect from "@/lib/mongodb"
import { getAuthUser } from "@/lib/auth-utils"
import { ensureModelsRegistered } from "@/lib/models"
import Notification from "@/models/Notification"

// Ensure models are registered
ensureModelsRegistered()

// Schema cho query params
const querySchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(10),
  read: z.enum(["true", "false", "all"]).optional().default("all"),
  type: z.enum(["order", "inventory", "payment", "system", "all"]).optional().default("all"),
})

export async function GET(request: NextRequest) {
  try {
    await dbConnect()

    // L<PERSON><PERSON> thông tin người dùng từ token
    const user = await getAuthUser(request)

    if (!user) {
      return NextResponse.json(
        { error: "<PERSON>hông có quyền truy cập" },
        { status: 401 }
      )
    }

    // Lấy query params
    const { searchParams } = new URL(request.url)
    const page = searchParams.get("page") || "1"
    const limit = searchParams.get("limit") || "10"
    const read = searchParams.get("read") || "all"
    const type = searchParams.get("type") || "all"

    // Validate query params
    const validatedParams = querySchema.parse({
      page,
      limit,
      read,
      type,
    })

    // Tạo query filter
    const filter: any = { user: user._id }

    // Lọc theo trạng thái đã đọc
    if (validatedParams.read !== "all") {
      filter.read = validatedParams.read === "true"
    }

    // Lọc theo loại thông báo
    if (validatedParams.type !== "all") {
      filter.type = validatedParams.type
    }

    // Tính toán pagination
    const skip = (validatedParams.page - 1) * validatedParams.limit

    // Lấy tổng số thông báo
    const total = await Notification.countDocuments(filter)

    // Lấy danh sách thông báo
    const notifications = await Notification.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(validatedParams.limit)

    // Lấy số lượng thông báo chưa đọc
    const unreadCount = await Notification.countDocuments({
      user: user._id,
      read: false,
    })

    return NextResponse.json({
      notifications,
      unreadCount,
      pagination: {
        total,
        page: validatedParams.page,
        limit: validatedParams.limit,
        totalPages: Math.ceil(total / validatedParams.limit),
      },
    })
  } catch (error) {
    console.error("Error fetching notifications:", error)
    return NextResponse.json(
      { error: "Lỗi lấy danh sách thông báo" },
      { status: 500 }
    )
  }
}

// Tạo thông báo mới
export async function POST(request: NextRequest) {
  try {
    await dbConnect()

    // Lấy thông tin người dùng từ token
    const user = await getAuthUser(request)

    if (!user) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      )
    }

    // Chỉ admin và seller mới có thể tạo thông báo
    if (user.role !== "admin" && user.role !== "seller") {
      return NextResponse.json(
        { error: "Không có quyền tạo thông báo" },
        { status: 403 }
      )
    }

    // Lấy dữ liệu từ request body
    const body = await request.json()

    // Validate dữ liệu
    const notificationSchema = z.object({
      userId: z.string(),
      title: z.string().min(1),
      message: z.string().min(1),
      type: z.enum(["order", "inventory", "payment", "system"]).default("system"),
      data: z.record(z.any()).optional(),
    })

    const validatedData = notificationSchema.parse(body)

    // Tạo thông báo mới
    const notification = new Notification({
      user: validatedData.userId,
      title: validatedData.title,
      message: validatedData.message,
      type: validatedData.type,
      data: validatedData.data,
      read: false,
    })

    await notification.save()

    return NextResponse.json(notification, { status: 201 })
  } catch (error) {
    console.error("Error creating notification:", error)
    return NextResponse.json(
      { error: "Lỗi tạo thông báo" },
      { status: 500 }
    )
  }
}
