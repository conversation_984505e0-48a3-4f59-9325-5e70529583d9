import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import dbConnect from "@/lib/mongodb"
import { getAuthUser } from "@/lib/auth-utils"
import { ensureModelsRegistered } from "@/lib/models"
import Shop from "@/models/Shop"
import Order from "@/models/Order"
import Product from "@/models/Product"

// Ensure models are registered
ensureModelsRegistered()

// Schema cho request body
const reportSchema = z.object({
  type: z.enum(["revenue", "products", "inventory"]),
  timeRange: z.enum(["day", "week", "month", "year", "custom"]),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  filters: z.record(z.any()).optional(),
})

export async function POST(request: NextRequest) {
  try {
    await dbConnect()

    // Lấy thông tin người dùng từ token
    const user = await getAuthUser(request)

    if (!user) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      )
    }

    // Chỉ seller và admin mới có thể tạo báo cáo
    if (user.role !== "seller" && user.role !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền tạo báo cáo" },
        { status: 403 }
      )
    }

    // Lấy shop của seller
    const shop = await Shop.findOne({ owner: user._id })

    if (!shop && user.role === "seller") {
      return NextResponse.json(
        { error: "Không tìm thấy shop" },
        { status: 404 }
      )
    }

    // Lấy dữ liệu từ request body
    const body = await request.json()

    // Validate dữ liệu
    const validatedData = reportSchema.parse(body)

    // Xác định khoảng thời gian
    let startDate: Date, endDate: Date
    const now = new Date()

    if (validatedData.timeRange === "custom") {
      if (!validatedData.startDate || !validatedData.endDate) {
        return NextResponse.json(
          { error: "Cần cung cấp startDate và endDate cho timeRange custom" },
          { status: 400 }
        )
      }
      startDate = new Date(validatedData.startDate)
      endDate = new Date(validatedData.endDate)
    } else {
      endDate = new Date()
      
      switch (validatedData.timeRange) {
        case "day":
          startDate = new Date(now.setHours(0, 0, 0, 0))
          break
        case "week":
          startDate = new Date(now)
          startDate.setDate(now.getDate() - 7)
          break
        case "month":
          startDate = new Date(now)
          startDate.setMonth(now.getMonth() - 1)
          break
        case "year":
          startDate = new Date(now)
          startDate.setFullYear(now.getFullYear() - 1)
          break
        default:
          startDate = new Date(now)
          startDate.setDate(now.getDate() - 7)
      }
    }

    // Tạo báo cáo dựa vào loại
    let reportData: any = {}
    
    switch (validatedData.type) {
      case "revenue":
        reportData = await generateRevenueReport(user, shop, startDate, endDate, validatedData.filters)
        break
      case "products":
        reportData = await generateProductsReport(user, shop, startDate, endDate, validatedData.filters)
        break
      case "inventory":
        reportData = await generateInventoryReport(user, shop, validatedData.filters)
        break
      default:
        return NextResponse.json(
          { error: "Loại báo cáo không hợp lệ" },
          { status: 400 }
        )
    }

    return NextResponse.json({
      type: validatedData.type,
      timeRange: validatedData.timeRange,
      startDate,
      endDate,
      generatedAt: new Date(),
      data: reportData,
    })
  } catch (error) {
    console.error("Error generating report:", error)
    return NextResponse.json(
      { error: "Lỗi tạo báo cáo" },
      { status: 500 }
    )
  }
}

// Hàm tạo báo cáo doanh thu
async function generateRevenueReport(user: any, shop: any, startDate: Date, endDate: Date, filters?: any) {
  // Tạo query filter
  const filter: any = {
    createdAt: { $gte: startDate, $lte: endDate },
  }

  // Nếu là seller, chỉ lấy đơn hàng của shop
  if (user.role === "seller" && shop) {
    filter["items.product"] = { $in: await Product.find({ shop: shop._id }).distinct("_id") }
  }

  // Thêm các filter khác nếu có
  if (filters) {
    if (filters.status) {
      filter.status = filters.status
    }
    if (filters.paymentMethod) {
      filter.paymentMethod = filters.paymentMethod
    }
  }

  // Lấy danh sách đơn hàng
  const orders = await Order.find(filter)
    .populate("items.product")
    .populate("user", "name email")

  // Tính toán doanh thu
  let totalRevenue = 0
  let ordersByDay: Record<string, { date: string; revenue: number; orders: number }> = {}
  let ordersByStatus: Record<string, number> = {}
  let paymentMethods: Record<string, number> = {}

  orders.forEach((order) => {
    // Tính tổng doanh thu
    let orderRevenue = 0
    order.items.forEach((item: any) => {
      if (user.role === "admin" || (item.product && item.product.shop && item.product.shop.toString() === shop._id.toString())) {
        orderRevenue += item.price * item.quantity
      }
    })
    totalRevenue += orderRevenue

    // Thống kê theo ngày
    const dateStr = order.createdAt.toISOString().split("T")[0]
    if (!ordersByDay[dateStr]) {
      ordersByDay[dateStr] = { date: dateStr, revenue: 0, orders: 0 }
    }
    ordersByDay[dateStr].revenue += orderRevenue
    ordersByDay[dateStr].orders += 1

    // Thống kê theo trạng thái
    if (!ordersByStatus[order.status]) {
      ordersByStatus[order.status] = 0
    }
    ordersByStatus[order.status] += 1

    // Thống kê theo phương thức thanh toán
    if (!paymentMethods[order.paymentMethod]) {
      paymentMethods[order.paymentMethod] = 0
    }
    paymentMethods[order.paymentMethod] += 1
  })

  // Chuyển đổi ordersByDay thành mảng và sắp xếp theo ngày
  const dailyRevenue = Object.values(ordersByDay).sort((a, b) => a.date.localeCompare(b.date))

  return {
    totalRevenue,
    totalOrders: orders.length,
    averageOrderValue: orders.length > 0 ? totalRevenue / orders.length : 0,
    dailyRevenue,
    ordersByStatus,
    paymentMethods,
  }
}

// Hàm tạo báo cáo sản phẩm
async function generateProductsReport(user: any, shop: any, startDate: Date, endDate: Date, filters?: any) {
  // Tạo query filter cho đơn hàng
  const orderFilter: any = {
    createdAt: { $gte: startDate, $lte: endDate },
  }

  // Tạo query filter cho sản phẩm
  const productFilter: any = {}

  // Nếu là seller, chỉ lấy sản phẩm của shop
  if (user.role === "seller" && shop) {
    productFilter.shop = shop._id
    orderFilter["items.product"] = { $in: await Product.find({ shop: shop._id }).distinct("_id") }
  }

  // Thêm các filter khác nếu có
  if (filters) {
    if (filters.category) {
      productFilter.category = filters.category
    }
  }

  // Lấy danh sách sản phẩm
  const products = await Product.find(productFilter)
    .populate("category", "name")

  // Lấy danh sách đơn hàng
  const orders = await Order.find(orderFilter)
    .populate("items.product")

  // Tính toán doanh số cho từng sản phẩm
  const productSales: Record<string, { 
    _id: string, 
    name: string, 
    sku: string,
    category: string,
    price: number,
    quantity: number,
    revenue: number,
    orders: number
  }> = {}

  products.forEach(product => {
    productSales[product._id.toString()] = {
      _id: product._id.toString(),
      name: product.title,
      sku: product.sku || "",
      category: product.category ? product.category.name : "",
      price: product.price,
      quantity: 0,
      revenue: 0,
      orders: 0
    }
  })

  // Tính toán doanh số
  orders.forEach(order => {
    order.items.forEach((item: any) => {
      if (item.product && productSales[item.product._id.toString()]) {
        productSales[item.product._id.toString()].quantity += item.quantity
        productSales[item.product._id.toString()].revenue += item.price * item.quantity
        productSales[item.product._id.toString()].orders += 1
      }
    })
  })

  // Chuyển đổi productSales thành mảng và sắp xếp theo doanh thu
  const productSalesArray = Object.values(productSales).sort((a, b) => b.revenue - a.revenue)

  // Tính toán tổng doanh thu và số lượng
  const totalRevenue = productSalesArray.reduce((sum, product) => sum + product.revenue, 0)
  const totalQuantity = productSalesArray.reduce((sum, product) => sum + product.quantity, 0)

  // Tính toán top 5 sản phẩm bán chạy
  const topProducts = productSalesArray.slice(0, 5)

  return {
    totalProducts: products.length,
    totalRevenue,
    totalQuantity,
    topProducts,
    productSales: productSalesArray,
  }
}

// Hàm tạo báo cáo tồn kho
async function generateInventoryReport(user: any, shop: any, filters?: any) {
  // Tạo query filter
  const filter: any = {}

  // Nếu là seller, chỉ lấy sản phẩm của shop
  if (user.role === "seller" && shop) {
    filter.shop = shop._id
  }

  // Thêm các filter khác nếu có
  if (filters) {
    if (filters.category) {
      filter.category = filters.category
    }
    if (filters.lowStock) {
      filter.stock = { $lte: filters.lowStock }
    }
  }

  // Lấy danh sách sản phẩm
  const products = await Product.find(filter)
    .populate("category", "name")
    .sort({ stock: 1 })

  // Tính toán tổng giá trị tồn kho
  const totalStockValue = products.reduce((sum, product) => sum + (product.price * product.stock), 0)

  // Tìm sản phẩm có tồn kho thấp
  const lowStockThreshold = 10 // Ngưỡng mặc định
  const lowStockProducts = products.filter(product => {
    const threshold = product.minStockThreshold || lowStockThreshold
    return product.stock <= threshold
  })

  // Phân loại sản phẩm theo danh mục
  const categorySummary: Record<string, { 
    category: string, 
    productCount: number, 
    totalStock: number, 
    totalValue: number 
  }> = {}

  products.forEach(product => {
    const categoryName = product.category ? product.category.name : "Không phân loại"
    if (!categorySummary[categoryName]) {
      categorySummary[categoryName] = {
        category: categoryName,
        productCount: 0,
        totalStock: 0,
        totalValue: 0
      }
    }
    categorySummary[categoryName].productCount += 1
    categorySummary[categoryName].totalStock += product.stock
    categorySummary[categoryName].totalValue += product.price * product.stock
  })

  // Chuyển đổi categorySummary thành mảng
  const categorySummaryArray = Object.values(categorySummary)

  return {
    totalProducts: products.length,
    totalStock: products.reduce((sum, product) => sum + product.stock, 0),
    totalStockValue,
    lowStockProducts: lowStockProducts.map(product => ({
      _id: product._id,
      name: product.title,
      sku: product.sku || "",
      category: product.category ? product.category.name : "Không phân loại",
      stock: product.stock,
      minStockThreshold: product.minStockThreshold || lowStockThreshold,
      price: product.price,
      value: product.price * product.stock
    })),
    categorySummary: categorySummaryArray,
    products: products.map(product => ({
      _id: product._id,
      name: product.title,
      sku: product.sku || "",
      category: product.category ? product.category.name : "Không phân loại",
      stock: product.stock,
      minStockThreshold: product.minStockThreshold || lowStockThreshold,
      price: product.price,
      value: product.price * product.stock
    }))
  }
}
