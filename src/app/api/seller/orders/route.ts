import { NextResponse } from 'next/server';
import { z } from 'zod';
import dbConnect from '@/lib/mongodb';
import { ensureModelsRegistered, Order, Shop } from '@/lib/models';
import { getAuthUser } from '@/lib/auth-utils';

export async function GET(request: Request) {
  try {
    await dbConnect();

    // Đảm bảo tất cả các models được đăng ký
    ensureModelsRegistered();

    const user = await getAuthUser(request);

    if (!user) {
      return NextResponse.json(
        { error: 'Không có quyền truy cập' },
        { status: 401 }
      );
    }

    // Chỉ seller hoặc admin mới có thể xem danh sách đơn hàng
    if (user.role !== 'seller' && user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Không có quyền xem danh sách đơn hàng' },
        { status: 403 }
      );
    }

    // L<PERSON>y tham số từ URL
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const skip = (page - 1) * limit;

    // Tìm shop của seller
    const shop = await Shop.findOne({ owner: user._id });

    if (!shop && user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Không tìm thấy cửa hàng' },
        { status: 404 }
      );
    }

    // Tạo query filter
    const filter: any = {};
    
    // Nếu là admin, có thể xem tất cả đơn hàng
    // Nếu là seller, chỉ xem đơn hàng của shop mình
    if (user.role === 'seller' && shop) {
      // Lấy danh sách đơn hàng có chứa sản phẩm của shop
      filter['items.product'] = { $in: shop.products };
    }

    if (status) {
      filter.status = status;
    }

    // Tạo sort options
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Lấy danh sách đơn hàng
    const total = await Order.countDocuments(filter);
    const orders = await Order.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('items.product', 'title price images')
      .populate('user', 'name email');

    return NextResponse.json({
      orders,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });
  } catch (error) {
    console.error('Error fetching seller orders:', error);
    return NextResponse.json(
      { error: 'Lỗi lấy danh sách đơn hàng' },
      { status: 500 }
    );
  }
}
