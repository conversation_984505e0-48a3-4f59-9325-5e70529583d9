import { NextRequest, NextResponse } from "next/server"
import mongoose from "mongoose"
import dbConnect from "@/lib/mongodb"
// import { getAuthUser } from "@/lib/auth-utils"
import { getAuthUser } from "@/lib/auth-utils-temp" // Tạm thời bỏ qua xác thực
import { slugify } from "@/lib/utils"
import { ensureModelsRegistered } from "@/lib/models"
import Product from "@/models/Product"
import Shop from "@/models/Shop"

// Ensure models are registered
ensureModelsRegistered()

export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: "Invalid product ID" },
        { status: 400 }
      )
    }

    // Get the seller's shop
    const shop = await Shop.findOne({ owner: user._id })

    if (!shop) {
      return NextResponse.json(
        { error: "Shop not found" },
        { status: 404 }
      )
    }

    // Find the original product
    const originalProduct = await Product.findOne({
      _id: params.id,
      shop: shop._id
    })

    if (!originalProduct) {
      return NextResponse.json(
        { error: "Product not found or you don't have permission to duplicate it" },
        { status: 404 }
      )
    }

    // Create a new product based on the original
    const productData = originalProduct.toObject()

    // Remove _id to create a new document
    delete productData._id

    // Update title and slug
    productData.title = `${productData.title} (Copy)`
    productData.slug = slugify(productData.title)

    // Check if a product with this slug already exists
    const existingProduct = await Product.findOne({ slug: productData.slug, shop: shop._id })
    if (existingProduct) {
      // Add timestamp to make slug unique
      const timestamp = Date.now()
      productData.slug = `${productData.slug}-${timestamp}`
    }

    // Create the new product
    const newProduct = new Product(productData)
    await newProduct.save()

    // Populate category and shop information before returning
    await newProduct.populate('category', 'name slug')
    await newProduct.populate('shop', 'name logo')

    return NextResponse.json(newProduct, { status: 201 })
  } catch (error) {
    console.error("Error duplicating product:", error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return NextResponse.json(
      { error: "Failed to duplicate product", details: errorMessage },
      { status: 500 }
    )
  }
}
