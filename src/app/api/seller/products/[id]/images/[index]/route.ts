import { NextRequest, NextResponse } from "next/server"
import mongoose from "mongoose"
import dbConnect from "@/lib/mongodb"
// import { getAuthUser } from "@/lib/auth-utils"
import { getAuthUser } from "@/lib/auth-utils-temp" // Tạm thời bỏ qua xác thực
import { ensureModelsRegistered } from "@/lib/models"
import Product from "@/models/Product"
import Shop from "@/models/Shop"

// Ensure models are registered
ensureModelsRegistered()

// DELETE: X<PERSON>a một hình ảnh cụ thể của sản phẩm
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string; index: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: "Invalid product ID" },
        { status: 400 }
      )
    }

    // Validate index
    const index = parseInt(params.index)
    if (isNaN(index) || index < 0) {
      return NextResponse.json(
        { error: "Invalid image index" },
        { status: 400 }
      )
    }

    // Get the seller's shop
    const shop = await Shop.findOne({ owner: user._id })

    if (!shop) {
      return NextResponse.json(
        { error: "Shop not found" },
        { status: 404 }
      )
    }

    // Find the product
    const product = await Product.findOne({
      _id: params.id,
      shop: shop._id
    })

    if (!product) {
      return NextResponse.json(
        { error: "Product not found or you don't have permission to update it" },
        { status: 404 }
      )
    }

    // Check if index is valid
    if (index >= product.images.length) {
      return NextResponse.json(
        { error: "Image index out of bounds" },
        { status: 400 }
      )
    }

    // Remove the image at the specified index
    product.images.splice(index, 1)
    await product.save()

    return NextResponse.json({
      success: true,
      message: "Image deleted successfully",
      images: product.images
    })
  } catch (error) {
    console.error("Error deleting product image:", error)
    return NextResponse.json(
      { error: "Failed to delete product image" },
      { status: 500 }
    )
  }
}
