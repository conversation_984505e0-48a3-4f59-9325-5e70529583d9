import { NextRequest, NextResponse } from "next/server"
import { ensureModelsRegistered } from "@/lib/models"
import Product from "@/models/Product"
import Shop from "@/models/Shop"
import Review from "@/models/Review"
import InventoryHistory from "@/models/InventoryHistory"
import dbConnect from "@/lib/mongodb"
// import { getAuthUser } from "@/lib/auth-utils"
import { getAuthUser } from "@/lib/auth-utils-temp" // Tạm thời bỏ qua xác thực
import mongoose from "mongoose"
import { z } from "zod"

// Ensure models are registered
ensureModelsRegistered()

// Schema validation cho cập nhật sản phẩm
const updateProductSchema = z.object({
  title: z.string().min(3, "Tên sản phẩm phải có ít nhất 3 ký tự").optional(),
  description: z.string().min(10, "<PERSON><PERSON> tả sản phẩm phải có ít nhất 10 ký tự").optional(),
  price: z.number().min(0, "Gi<PERSON> sản phẩm không được âm").optional(),
  discountPrice: z.number().min(0, "Giá khuyến mãi không được âm").optional().nullable(),
  stock: z.number().min(0, "Số lượng tồn kho không được âm").optional(),
  category: z.string().optional(),
  featured: z.boolean().optional(),
  variants: z
    .array(
      z.object({
        name: z.string().min(1, "Tên biến thể không được để trống"),
        price: z.number().min(0, "Giá biến thể không được âm"),
        stock: z.number().min(0, "Số lượng tồn kho biến thể không được âm"),
        attributes: z.record(z.string()).default({}),
      })
    )
    .optional(),
  specifications: z.record(z.string()).optional(),
  minStockThreshold: z.number().min(0, "Ngưỡng tồn kho tối thiểu không được âm").optional(),
})

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get the seller's shop
    let shop = await Shop.findOne({ owner: user._id })

    // If shop doesn't exist, create a default one
    if (!shop) {
      const shopName = `${user.name}'s Shop`
      const { slugify } = await import('@/lib/utils')

      shop = new Shop({
        name: shopName,
        slug: slugify(shopName),
        description: `Welcome to ${shopName}`,
        logo: "",
        banner: "",
        owner: user._id,
      })

      await shop.save()
    }

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: "Invalid product ID" },
        { status: 400 }
      )
    }

    // Get the product
    const product = await Product.findOne({
      _id: params.id,
      shop: shop._id,
    }).populate("category")

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      )
    }

    return NextResponse.json(product)
  } catch (error) {
    console.error("Error fetching product:", error)
    return NextResponse.json(
      { error: "Failed to fetch product" },
      { status: 500 }
    )
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get the seller's shop
    let shop = await Shop.findOne({ owner: user._id })

    // If shop doesn't exist, create a default one
    if (!shop) {
      const shopName = `${user.name}'s Shop`
      const { slugify } = await import('@/lib/utils')

      shop = new Shop({
        name: shopName,
        slug: slugify(shopName),
        description: `Welcome to ${shopName}`,
        logo: "",
        banner: "",
        owner: user._id,
      })

      await shop.save()
    }

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: "Invalid product ID" },
        { status: 400 }
      )
    }

    // Parse and validate request body
    const data = await req.json()

    try {
      // Chuyển đổi các trường số từ string sang number nếu cần
      if (typeof data.price === 'string') data.price = parseFloat(data.price);
      if (typeof data.discountPrice === 'string') data.discountPrice = parseFloat(data.discountPrice);
      if (typeof data.stock === 'string') data.stock = parseInt(data.stock);
      if (typeof data.minStockThreshold === 'string') data.minStockThreshold = parseInt(data.minStockThreshold);

      updateProductSchema.parse(data)
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Validation error", details: error.errors },
          { status: 400 }
        )
      }
      throw error
    }

    // Find the product to check if stock has changed
    const existingProduct = await Product.findOne({
      _id: params.id,
      shop: shop._id
    })

    if (!existingProduct) {
      return NextResponse.json(
        { error: "Product not found or you don't have permission to update it" },
        { status: 404 }
      )
    }

    // Check if stock has changed
    if (data.stock !== undefined && data.stock !== existingProduct.stock) {
      // Create inventory history entry
      const historyEntry = new InventoryHistory({
        product: existingProduct._id,
        shop: shop._id,
        user: user._id,
        actionType: data.stock > existingProduct.stock ? "add" : "remove",
        quantity: Math.abs(data.stock - existingProduct.stock),
        previousQuantity: existingProduct.stock,
        newQuantity: data.stock,
        notes: "Stock updated via product edit"
      })

      await historyEntry.save()
    }

    // Update the product
    const product = await Product.findOneAndUpdate(
      {
        _id: params.id,
        shop: shop._id,
      },
      { $set: data },
      { new: true }
    ).populate("category", "name slug")

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      )
    }

    return NextResponse.json(product)
  } catch (error) {
    console.error("Error updating product:", error)
    return NextResponse.json(
      { error: "Failed to update product" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get the seller's shop
    let shop = await Shop.findOne({ owner: user._id })

    // If shop doesn't exist, create a default one
    if (!shop) {
      const shopName = `${user.name}'s Shop`
      const { slugify } = await import('@/lib/utils')

      shop = new Shop({
        name: shopName,
        slug: slugify(shopName),
        description: `Welcome to ${shopName}`,
        logo: "",
        banner: "",
        owner: user._id,
      })

      await shop.save()
    }

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: "Invalid product ID" },
        { status: 400 }
      )
    }

    // Find the product first to make sure it exists
    const product = await Product.findOne({
      _id: params.id,
      shop: shop._id
    })

    if (!product) {
      return NextResponse.json(
        { error: "Product not found or you don't have permission to delete it" },
        { status: 404 }
      )
    }

    // Delete the product
    await Product.deleteOne({ _id: params.id, shop: shop._id })

    // Delete related data (reviews, inventory history)
    await Review.deleteMany({ product: params.id })
    await InventoryHistory.deleteMany({ product: params.id })

    return NextResponse.json({
      success: true,
      message: "Product deleted successfully"
    })
  } catch (error) {
    console.error("Error deleting product:", error)
    return NextResponse.json(
      { error: "Failed to delete product" },
      { status: 500 }
    )
  }
}
