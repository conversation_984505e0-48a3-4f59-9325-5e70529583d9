import { NextRequest, NextResponse } from "next/server"
import mongoose from "mongoose"
import { z } from "zod"
import { ensureModelsRegistered } from "@/lib/models"
import Product from "@/models/Product"
import Shop from "@/models/Shop"
import dbConnect from "@/lib/mongodb"
// import { getAuthUser } from "@/lib/auth-utils"
import { getAuthUser } from "@/lib/auth-utils-temp" // Tạm thời bỏ qua xác thực
import { slugify } from "@/lib/utils"

// Ensure models are registered
ensureModelsRegistered()

// Schema validation cho sản phẩm
const productSchema = z.object({
  title: z.string().min(3, "Tên sản phẩm phải có ít nhất 3 ký tự"),
  description: z.string().min(10, "Mô tả sản phẩm phải có ít nhất 10 ký tự"),
  price: z.number().min(0, "<PERSON><PERSON><PERSON> sản phẩm không được âm"),
  discountPrice: z.number().min(0, "<PERSON><PERSON><PERSON> khuyến mãi không được âm").optional(),
  stock: z.number().min(0, "Số lượng tồn kho không được âm").default(0),
  category: z.string().optional(),
  images: z.array(z.string()).default([]),
  featured: z.boolean().default(false),
  variants: z
    .array(
      z.object({
        name: z.string().min(1, "Tên biến thể không được để trống"),
        price: z.number().min(0, "Giá biến thể không được âm"),
        stock: z.number().min(0, "Số lượng tồn kho biến thể không được âm"),
        attributes: z.record(z.string()).default({}),
      })
    )
    .optional(),
  specifications: z.record(z.string()).optional(),
  minStockThreshold: z.number().min(0, "Ngưỡng tồn kho tối thiểu không được âm").optional(),
})

export async function GET(req: NextRequest) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get the seller's shop
    let shop = await Shop.findOne({ owner: user._id })

    // If shop doesn't exist, create a default one
    if (!shop) {
      const shopName = `${user.name}'s Shop`
      const { slugify } = await import('@/lib/utils')

      shop = new Shop({
        name: shopName,
        slug: slugify(shopName),
        description: `Welcome to ${shopName}`,
        logo: "",
        banner: "",
        owner: user._id,
      })

      await shop.save()
    }

    // Get query parameters for pagination
    const { searchParams } = new URL(req.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // Get total count
    const total = await Product.countDocuments({ shop: shop._id })

    // Get products with pagination
    const products = await Product.find({ shop: shop._id })
      .populate("category", "name slug")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)

    return NextResponse.json({
      products,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    })
  } catch (error) {
    console.error("Error fetching seller products:", error)
    return NextResponse.json(
      { error: "Failed to fetch products" },
      { status: 500 }
    )
  }
}

export async function POST(req: NextRequest) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get the seller's shop
    let shop = await Shop.findOne({ owner: user._id })

    // If shop doesn't exist, create a default one
    if (!shop) {
      const shopName = `${user.name}'s Shop`
      const { slugify } = await import('@/lib/utils')

      shop = new Shop({
        name: shopName,
        slug: slugify(shopName),
        description: `Welcome to ${shopName}`,
        logo: "",
        banner: "",
        owner: user._id,
      })

      await shop.save()
    }

    // Parse and validate the request body
    const data = await req.json()
    console.log('Received product data:', data)

    try {
      // Chuyển đổi các trường số từ string sang number nếu cần
      if (typeof data.price === 'string') data.price = parseFloat(data.price);
      if (typeof data.discountPrice === 'string') data.discountPrice = parseFloat(data.discountPrice);
      if (typeof data.stock === 'string') data.stock = parseInt(data.stock);
      if (typeof data.minStockThreshold === 'string') data.minStockThreshold = parseInt(data.minStockThreshold);

      productSchema.parse(data)
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Validation error", details: error.errors },
          { status: 400 }
        )
      }
      throw error
    }

    // Generate slug from title
    const slug = slugify(data.title || 'product')

    // Check if a product with this slug already exists for this shop
    const existingProduct = await Product.findOne({ slug, shop: shop._id })
    if (existingProduct) {
      return NextResponse.json(
        { error: "A product with this name already exists" },
        { status: 400 }
      );
    }

    // Find or create a default category if not provided
    let categoryId = data.category
    console.log('Category ID from request:', categoryId, typeof categoryId)

    // If categoryId is a string but not a valid ObjectId, we need to find the category by name or slug
    if (categoryId && typeof categoryId === 'string' && categoryId.length < 24) {
      console.log('Category ID is not a valid ObjectId, trying to find by name or slug')
      // Import Category model
      const Category = (await import('@/models/Category')).default

      // Try to find the category by name or slug
      const category = await Category.findOne({
        $or: [
          { name: categoryId },
          { slug: categoryId.toLowerCase() }
        ]
      })

      if (category) {
        console.log('Found category by name or slug:', category)
        categoryId = category._id
      } else {
        console.log('Could not find category by name or slug')
        categoryId = null
      }
    }

    if (!categoryId) {
      // Import Category model
      const Category = (await import('@/models/Category')).default

      // Find or create a default category
      let defaultCategory = await Category.findOne({ slug: 'uncategorized' })

      if (!defaultCategory) {
        defaultCategory = new Category({
          name: 'Uncategorized',
          slug: 'uncategorized',
          description: 'Default category for uncategorized products'
        })
        await defaultCategory.save()
      }

      categoryId = defaultCategory._id
    }

    // Create a new product
    const productData = {
      ...data,
      slug,
      shop: shop._id,
      category: categoryId || "uncategorized", // Use a default category ID if none provided
      stock: data.stock || 0,
      specifications: data.specifications || {},
      minStockThreshold: data.minStockThreshold || 5
    }
    console.log('Creating product with data:', productData)

    // Log the category ID for debugging
    console.log('Category ID being used:', productData.category, typeof productData.category)

    const product = new Product(productData)

    await product.save()

    // Populate category and shop information before returning
    await product.populate('category', 'name slug')
    await product.populate('shop', 'name logo')

    return NextResponse.json(product, { status: 201 })
  } catch (error) {
    console.error("Error creating product:", error);
    // Return more detailed error message for debugging
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: "Failed to create product", details: errorMessage },
      { status: 500 }
    );
  }
}
