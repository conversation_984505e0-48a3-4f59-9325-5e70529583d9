import { NextRequest, NextResponse } from "next/server"
import mongoose from "mongoose"
import { z } from "zod"
import dbConnect from "@/lib/mongodb"
// import { getAuthUser } from "@/lib/auth-utils"
import { getAuthUser } from "@/lib/auth-utils-temp" // Tạm thời bỏ qua xác thực
import { ensureModelsRegistered } from "@/lib/models"
import Product from "@/models/Product"
import Shop from "@/models/Shop"
import InventoryHistory from "@/models/InventoryHistory"

// Ensure models are registered
ensureModelsRegistered()

// Schema validation cho cập nhật tồn kho hàng loạt
const batchUpdateSchema = z.object({
  items: z.array(
    z.object({
      productId: z.string().refine(val => mongoose.Types.ObjectId.isValid(val), {
        message: "Invalid product ID"
      }),
      quantity: z.number().int(),
      actionType: z.enum(["add", "remove", "adjust"]),
      notes: z.string().optional()
    })
  ).min(1, "At least one item is required")
})

export async function POST(req: NextRequest) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get the seller's shop
    const shop = await Shop.findOne({ owner: user._id })

    if (!shop) {
      return NextResponse.json(
        { error: "Shop not found" },
        { status: 404 }
      )
    }

    // Parse and validate request body
    const data = await req.json()

    try {
      batchUpdateSchema.parse(data)
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Validation error", details: error.errors },
          { status: 400 }
        )
      }
      throw error
    }

    // Process each item in the batch
    const results = []
    const historyEntries = []

    for (const item of data.items) {
      // Find the product
      const product = await Product.findOne({
        _id: item.productId,
        shop: shop._id
      })

      if (!product) {
        results.push({
          productId: item.productId,
          success: false,
          error: "Product not found or you don't have permission to update it"
        })
        continue
      }

      // Calculate new quantity based on action type
      const previousQuantity = product.stock
      let newQuantity = previousQuantity

      switch (item.actionType) {
        case "add":
          newQuantity = previousQuantity + item.quantity
          break
        case "remove":
          newQuantity = Math.max(0, previousQuantity - item.quantity)
          break
        case "adjust":
          newQuantity = Math.max(0, item.quantity)
          break
      }

      // Update product stock
      product.stock = newQuantity
      await product.save()

      // Create history entry
      const historyEntry = new InventoryHistory({
        product: product._id,
        shop: shop._id,
        user: user._id,
        actionType: item.actionType,
        quantity: item.quantity,
        previousQuantity,
        newQuantity,
        notes: item.notes || ""
      })

      await historyEntry.save()
      historyEntries.push(historyEntry)

      // Add to results
      results.push({
        productId: item.productId,
        success: true,
        previousQuantity,
        newQuantity
      })
    }

    return NextResponse.json({
      success: true,
      results,
      historyEntries
    })
  } catch (error) {
    console.error("Error updating inventory batch:", error)
    return NextResponse.json(
      { error: "Failed to update inventory batch" },
      { status: 500 }
    )
  }
}
