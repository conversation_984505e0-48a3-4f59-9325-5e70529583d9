import { NextRequest, NextResponse } from "next/server"
import mongoose from "mongoose"
import dbConnect from "@/lib/mongodb"
// import { getAuthUser } from "@/lib/auth-utils"
import { getAuthUser } from "@/lib/auth-utils-temp" // Tạm thời bỏ qua xác thực
import { ensureModelsRegistered } from "@/lib/models"
import InventoryHistory from "@/models/InventoryHistory"
import Shop from "@/models/Shop"

// Ensure models are registered
ensureModelsRegistered()

export async function GET(req: NextRequest) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get the seller's shop
    const shop = await Shop.findOne({ owner: user._id })

    if (!shop) {
      return NextResponse.json(
        { error: "Shop not found" },
        { status: 404 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(req.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const productId = searchParams.get('productId')
    const actionType = searchParams.get('actionType')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    // Build query
    const query: any = { shop: shop._id }

    if (productId && mongoose.Types.ObjectId.isValid(productId)) {
      query.product = productId
    }

    if (actionType) {
      query.actionType = actionType
    }

    if (startDate || endDate) {
      query.createdAt = {}

      if (startDate) {
        query.createdAt.$gte = new Date(startDate)
      }

      if (endDate) {
        query.createdAt.$lte = new Date(endDate)
      }
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Get total count
    const total = await InventoryHistory.countDocuments(query)

    // Get history entries
    const history = await InventoryHistory.find(query)
      .populate('product', 'title slug images')
      .populate('user', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)

    return NextResponse.json({
      history,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    })
  } catch (error) {
    console.error("Error fetching inventory history:", error)
    return NextResponse.json(
      { error: "Failed to fetch inventory history" },
      { status: 500 }
    )
  }
}
