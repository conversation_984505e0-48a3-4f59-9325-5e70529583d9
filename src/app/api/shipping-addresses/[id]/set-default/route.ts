import { NextResponse } from 'next/server';
import { isValidObjectId } from 'mongoose';
import dbConnect from '@/lib/mongodb';
import ShippingAddress from '@/models/ShippingAddress';
import { getAuthUser } from '@/lib/auth-utils';

// Đặt địa chỉ giao hàng làm mặc định
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const { id } = params;

    // Kiểm tra ID hợp lệ
    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'ID không hợp lệ' },
        { status: 400 }
      );
    }

    // Xác thực người dùng
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Không có quyền truy cập' },
        { status: 401 }
      );
    }

    // Kiểm tra địa chỉ tồn tại và thuộc về người dùng
    const address = await ShippingAddress.findOne({
      _id: id,
      user: user._id,
    });

    if (!address) {
      return NextResponse.json(
        { error: 'Không tìm thấy địa chỉ giao hàng' },
        { status: 404 }
      );
    }

    // Đặt tất cả các địa chỉ khác không phải mặc định
    await ShippingAddress.updateMany(
      { user: user._id, _id: { $ne: id } },
      { $set: { isDefault: false } }
    );

    // Đặt địa chỉ này làm mặc định
    address.isDefault = true;
    await address.save();

    return NextResponse.json({
      success: true,
      message: 'Đã đặt địa chỉ làm mặc định',
      address,
    });
  } catch (error) {
    console.error('Error setting default shipping address:', error);
    return NextResponse.json(
      { error: 'Lỗi máy chủ nội bộ' },
      { status: 500 }
    );
  }
}
