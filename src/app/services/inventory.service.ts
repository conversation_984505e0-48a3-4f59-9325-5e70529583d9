import { fetchApi } from "./api"
import type { InventoryItem } from "@/store/seller/inventory/inventory.types"

interface InventoryResponse {
  items: InventoryItem[]
  total: number
  page: number
  limit: number
}

interface LowStockProductsResponse {
  products: Array<{
    _id: string
    title: string
    stock: number
    minStockThreshold: number
    sku: string
    image?: string
  }>
  total: number
}

interface CreateInventoryItemRequest {
  productName: string
  sku: string
  quantity: number
  unitPrice: number
  supplier: string
  notes?: string
  images: string[]
}

export const inventoryService = {
  getInventory: (page = 1, limit = 20) => fetchApi<InventoryResponse>(`/seller/inventory?page=${page}&limit=${limit}`),

  getInventoryItem: (id: string) => fetchApi<InventoryItem>(`/seller/inventory/${id}`),

  getLowStockProducts: (limit = 10) => fetchApi<LowStockProductsResponse>(`/seller/inventory/low-stock?limit=${limit}`),

  getInventoryHistory: (page = 1, limit = 20, filters?: Record<string, any>) => {
    let url = `/seller/inventory/history?page=${page}&limit=${limit}`

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          url += `&${key}=${encodeURIComponent(String(value))}`
        }
      })
    }

    return fetchApi(url)
  },

  createInventoryItem: (data: CreateInventoryItemRequest) =>
    fetchApi<InventoryItem>("/seller/inventory", {
      method: "POST",
      body: JSON.stringify(data),
    }),

  updateInventoryItem: (id: string, data: Partial<CreateInventoryItemRequest>) =>
    fetchApi<InventoryItem>(`/seller/inventory/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    }),

  updateStockBatch: (items: Array<{ productId: string, quantity: number, action: 'add' | 'subtract' | 'set' }>) =>
    fetchApi(`/seller/inventory/batch`, {
      method: "POST",
      body: JSON.stringify({ items }),
    }),

  updateStockThreshold: (productId: string, threshold: number) =>
    fetchApi(`/seller/inventory/${productId}/threshold`, {
      method: "PUT",
      body: JSON.stringify({ threshold }),
    }),

  deleteInventoryItem: (id: string) =>
    fetchApi<{ success: boolean }>(`/seller/inventory/${id}`, {
      method: "DELETE",
    }),
}

