import { fetchApi } from "./api"
import type { DailySales, ProductSales } from "@/types"

interface PaymentMethodDistribution {
  credit_card: number
  bank_transfer: number
  cash: number
}

interface OrderStatusDistribution {
  pending: number
  processing: number
  shipped: number
  delivered: number
  cancelled: number
}

interface CouponUsage {
  rate: number
  averageDiscount: number
  totalDiscount: number
  ordersWithCoupons: number
}

interface CategoryRevenue {
  category: string
  revenue: number
}

interface AdvancedKpis {
  averageRevenuePerCustomer: number
  repeatPurchaseRate: number
  categoryRevenue: CategoryRevenue[]
}

interface AnalyticsMetadata {
  timeFrame: string
  comparison: string
  category?: string
  startDate: string
  endDate: string
  generatedAt: string
}

interface AnalyticsResponse {
  totalRevenue: number
  revenueChange: number
  totalOrders: number
  ordersChange: number
  productsCount: number
  activeCustomers: number
  averageOrderValue: number
  conversionRate: number
  dailySales: DailySales[]
  productSales: ProductSales[]
  averageItemsPerOrder: number
  paymentMethods: PaymentMethodDistribution
  orderStatuses: OrderStatusDistribution
  couponUsage: CouponUsage
  advancedKpis: AdvancedKpis
  metadata: AnalyticsMetadata
}

export interface AnalyticsParams {
  timeFrame?: "day" | "3days" | "week" | "month" | "year"
  comparison?: "previous" | "year_ago" | "none"
  category?: string
}

export const analyticsService = {
  getAnalytics: (params: AnalyticsParams = {}) => {
    const { timeFrame = "week", comparison = "previous", category } = params
    let url = `/seller/analytics?timeFrame=${timeFrame}&comparison=${comparison}`

    if (category) {
      url += `&category=${encodeURIComponent(category)}`
    }

    return fetchApi<AnalyticsResponse>(url)
  },

  // Tạo báo cáo tùy chỉnh
  generateReport: (
    type: "revenue" | "products" | "inventory",
    timeRange: "day" | "week" | "month" | "year" | "custom",
    startDate?: string,
    endDate?: string,
    filters?: Record<string, any>
  ) => {
    return fetchApi(`/seller/reports/generate`, {
      method: "POST",
      body: JSON.stringify({
        type,
        timeRange,
        startDate,
        endDate,
        filters
      })
    })
  },

  // Xuất báo cáo
  exportReport: (
    type: "revenue" | "products" | "inventory",
    format: "csv" | "json",
    timeRange: "day" | "week" | "month" | "year" | "custom",
    startDate?: string,
    endDate?: string,
    filters?: Record<string, any>
  ) => {
    return fetchApi(`/seller/reports/export`, {
      method: "POST",
      body: JSON.stringify({
        type,
        format,
        timeRange,
        startDate,
        endDate,
        filters
      })
    })
  }
}
