"use client"

import { useRouter, usePathname } from "next/navigation"
import { useAuthState } from "@store/auth/auth.state"
import { useEffect } from "react"
import { toast } from "sonner"
import { useTranslation } from "react-i18next"

interface UseAuthRedirectOptions {
  redirectTo?: string
  redirectIfAuthenticated?: boolean
  redirectIfNotAuthenticated?: boolean
  message?: string
  showToast?: boolean
}

/**
 * Hook để xử lý chuyển hướng dựa trên trạng thái xác thực
 * 
 * @param options Tùy chọn chuyển hướng
 * @returns Trạng thái xác thực hiện tại
 */
export function useAuthRedirect(options: UseAuthRedirectOptions = {}) {
  const {
    redirectTo = "/login",
    redirectIfAuthenticated = false,
    redirectIfNotAuthenticated = false,
    message,
    showToast = true,
  } = options

  const { t } = useTranslation("common")
  const router = useRouter()
  const pathname = usePathname()
  const { isAuthenticated, user } = useAuthState()

  useEffect(() => {
    // Nếu đang ở trang đăng nhập hoặc đăng ký, không cần chuyển hướng
    if (pathname === "/login" || pathname === "/register") {
      return
    }

    // Nếu đã xác thực và cần chuyển hướng khi đã xác thực
    if (isAuthenticated && redirectIfAuthenticated) {
      if (showToast && message) {
        toast.info(message)
      }
      router.push(redirectTo)
      return
    }

    // Nếu chưa xác thực và cần chuyển hướng khi chưa xác thực
    if (!isAuthenticated && redirectIfNotAuthenticated) {
      if (showToast) {
        toast.error(message || t("auth.pleaseLogin"))
      }
      
      // Lưu URL hiện tại để chuyển hướng sau khi đăng nhập
      const callbackUrl = encodeURIComponent(pathname)
      router.push(`${redirectTo}?callbackUrl=${callbackUrl}`)
      return
    }
  }, [
    isAuthenticated,
    redirectIfAuthenticated,
    redirectIfNotAuthenticated,
    redirectTo,
    router,
    pathname,
    message,
    showToast,
    t,
  ])

  return { isAuthenticated, user }
}
