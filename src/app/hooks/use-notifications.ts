import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { notificationsService, type Notification, type NotificationSettings } from "@services/notifications.service"
import { useNotificationsState } from "@store/notifications/notifications.state"
import { useState, useEffect } from "react"

// Hook để lấy danh sách thông báo
export function useNotifications(
  page = 1,
  limit = 10,
  read: "true" | "false" | "all" = "all",
  type: "order" | "inventory" | "payment" | "system" | "all" = "all"
) {
  const notificationsState = useNotificationsState()
  const queryClient = useQueryClient()

  const query = useQuery({
    queryKey: ["notifications", page, limit, read, type],
    queryFn: () => notificationsService.getNotifications(page, limit, read, type),
    select: (data) => {
      // Cập nhật state trong Hookstate
      notificationsState.notifications = data.notifications
      notificationsState.unreadCount = data.unreadCount
      notificationsState.pagination = data.pagination
      return data
    },
  })

  // Mutation để đánh dấu thông báo đã đọc
  const markAsReadMutation = useMutation({
    mutationFn: (id: string) => notificationsService.markAsRead(id),
    onSuccess: () => {
      // Invalidate query để lấy lại danh sách thông báo
      queryClient.invalidateQueries({ queryKey: ["notifications"] })
    },
  })

  // Mutation để đánh dấu tất cả thông báo đã đọc
  const markAllAsReadMutation = useMutation({
    mutationFn: () => notificationsService.markAllAsRead(),
    onSuccess: () => {
      // Invalidate query để lấy lại danh sách thông báo
      queryClient.invalidateQueries({ queryKey: ["notifications"] })
    },
  })

  return {
    ...query,
    markAsRead: markAsReadMutation.mutate,
    markAllAsRead: markAllAsReadMutation.mutate,
    isMarkingAsRead: markAsReadMutation.isPending,
    isMarkingAllAsRead: markAllAsReadMutation.isPending,
  }
}

// Hook để lấy và cập nhật cài đặt thông báo
export function useNotificationSettings() {
  const notificationsState = useNotificationsState()
  const queryClient = useQueryClient()

  const query = useQuery({
    queryKey: ["notificationSettings"],
    queryFn: () => notificationsService.getSettings(),
    select: (data) => {
      // Cập nhật state trong Hookstate
      notificationsState.settings = data
      return data
    },
  })

  // Mutation để cập nhật cài đặt thông báo
  const updateSettingsMutation = useMutation({
    mutationFn: (settings: Partial<NotificationSettings>) => notificationsService.updateSettings(settings),
    onSuccess: (data) => {
      // Cập nhật state trong Hookstate
      notificationsState.settings = data
      // Invalidate query để lấy lại cài đặt thông báo
      queryClient.invalidateQueries({ queryKey: ["notificationSettings"] })
    },
  })

  return {
    ...query,
    updateSettings: updateSettingsMutation.mutate,
    isUpdating: updateSettingsMutation.isPending,
  }
}

// Hook để lắng nghe thông báo mới (giả lập)
export function useNotificationListener() {
  const queryClient = useQueryClient()
  const [hasNewNotification, setHasNewNotification] = useState(false)

  // Giả lập lắng nghe thông báo mới
  useEffect(() => {
    // Trong thực tế, đây sẽ là kết nối WebSocket hoặc SSE
    const interval = setInterval(() => {
      // Giả lập có thông báo mới với xác suất 10%
      if (Math.random() < 0.1) {
        setHasNewNotification(true)
        // Invalidate query để lấy lại danh sách thông báo
        queryClient.invalidateQueries({ queryKey: ["notifications"] })
      }
    }, 30000) // Kiểm tra mỗi 30 giây

    return () => clearInterval(interval)
  }, [queryClient])

  return {
    hasNewNotification,
    clearNewNotificationFlag: () => setHasNewNotification(false),
  }
}
