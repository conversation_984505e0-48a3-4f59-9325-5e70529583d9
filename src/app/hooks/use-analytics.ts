import { useQuery, useMutation } from "@tanstack/react-query"
import { analyticsService, type AnalyticsParams } from "@services/analytics.service"
import { useAnalyticsState } from "@store/seller/analytics/analytics.state"
import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

export function useAnalytics(
  timeFrameOrParams: AnalyticsParams | "day" | "3days" | "week" | "month" | "year" = "week"
) {
  const analyticsState = useAnalyticsState()
  const router = useRouter()

  // Chuyển đổi tham số thành đối tượng AnalyticsParams
  const params: AnalyticsParams = typeof timeFrameOrParams === "string"
    ? { timeFrame: timeFrameOrParams }
    : timeFrameOrParams

  const { timeFrame = "week", comparison = "previous", category } = params

  const query = useQuery({
    queryKey: ["analytics", timeFrame, comparison, category],
    queryFn: async () => {
      try {
        return await analyticsService.getAnalytics(params)
      } catch (error) {
        // Check if error is unauthorized
        if (error instanceof Error && error.message.includes('Unauthorized')) {
          toast.error('Session expired. Please log in again.')
          // Redirect to login after a short delay
          setTimeout(() => {
            router.push('/login')
          }, 1500)
        }
        throw error
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1, // Only retry once for failed requests
  })

  // Update state after data is fetched successfully
  useEffect(() => {
    if (query.data) {
      analyticsState.dailySales = query.data.dailySales || []
      analyticsState.productSales = query.data.productSales || []
      analyticsState.totalRevenue = query.data.totalRevenue || 0
      analyticsState.revenueChange = query.data.revenueChange || 0
      analyticsState.totalOrders = query.data.totalOrders || 0
      analyticsState.ordersChange = query.data.ordersChange || 0
      analyticsState.productsCount = query.data.productsCount || 0
      analyticsState.activeCustomers = query.data.activeCustomers || 0
      analyticsState.averageOrderValue = query.data.averageOrderValue || 0
      analyticsState.conversionRate = query.data.conversionRate || 0

      // Update new metrics
      if (query.data.averageItemsPerOrder !== undefined) {
        analyticsState.averageItemsPerOrder = query.data.averageItemsPerOrder
      }

      if (query.data.paymentMethods !== undefined) {
        analyticsState.paymentMethods = query.data.paymentMethods
      }

      if (query.data.orderStatuses !== undefined) {
        analyticsState.orderStatuses = query.data.orderStatuses
      }

      if (query.data.couponUsage !== undefined) {
        analyticsState.couponUsage = query.data.couponUsage
      }

      // Update advanced KPIs if available
      if (query.data.advancedKpis !== undefined) {
        analyticsState.advancedKpis = query.data.advancedKpis
      }

      // Update metadata if available
      if (query.data.metadata !== undefined) {
        analyticsState.metadata = query.data.metadata
      }

      analyticsState.timeFrame = typeof timeFrameOrParams === "string"
        ? timeFrameOrParams
        : timeFrameOrParams.timeFrame || "week"
    }
  }, [query.data, timeFrameOrParams, analyticsState])

  // Mutation để tạo báo cáo tùy chỉnh
  const generateReportMutation = useMutation({
    mutationFn: (params: {
      type: "revenue" | "products" | "inventory",
      timeRange: "day" | "week" | "month" | "year" | "custom",
      startDate?: string,
      endDate?: string,
      filters?: Record<string, any>
    }) => {
      return analyticsService.generateReport(
        params.type,
        params.timeRange,
        params.startDate,
        params.endDate,
        params.filters
      )
    }
  })

  // Mutation để xuất báo cáo
  const exportReportMutation = useMutation({
    mutationFn: (params: {
      type: "revenue" | "products" | "inventory",
      format: "csv" | "json",
      timeRange: "day" | "week" | "month" | "year" | "custom",
      startDate?: string,
      endDate?: string,
      filters?: Record<string, any>
    }) => {
      return analyticsService.exportReport(
        params.type,
        params.format,
        params.timeRange,
        params.startDate,
        params.endDate,
        params.filters
      )
    }
  })

  return {
    ...query,
    generateReport: generateReportMutation.mutate,
    isGeneratingReport: generateReportMutation.isPending,
    generatedReport: generateReportMutation.data,
    exportReport: exportReportMutation.mutate,
    isExportingReport: exportReportMutation.isPending
  }
}

