# ZenBuy - Nền tảng Thương mại Điện tử

ZenBuy là một nền tảng thương mại điện tử hiện đại được xây dựng với Next.js, MongoDB và nhiều công nghệ tiên tiến khác. Dự án này cung cấp trải nghiệm mua sắm trực tuyến đầy đủ tính năng cho cả người mua và người bán.

![ZenBuy Logo](public/images/logo.png)

## 📋 Mục lục

- [Tính năng chính](#tính-năng-chính)
- [Công nghệ sử dụng](#công-nghệ-sử-dụng)
- [Cấu trúc dự án](#cấu-trúc-dự-án)
- [Luồng hoạt động](#luồng-hoạt-động)
- [Tính năng đang phát triển](#tính-năng-đang-phát-triển)
- [Cài đặt và chạy](#cài-đặt-và-chạy)
- [Triển khai](#triển-khai)
- [Đa ngôn ngữ](#đa-ngôn-ngữ)
- [Tác giả](#tác-giả)

## ✨ Tính năng chính

### Dành cho người mua
- **Trang chủ**: Hiển thị sản phẩm nổi bật, danh mục sản phẩm
- **Tìm kiếm sản phẩm**: Tìm kiếm với bộ lọc nâng cao (danh mục, giá, thương hiệu)
- **Chi tiết sản phẩm**: Xem thông tin chi tiết, hình ảnh, đánh giá
- **Giỏ hàng**: Thêm, cập nhật, xóa sản phẩm
- **Thanh toán**: Quy trình thanh toán đơn giản với nhiều phương thức
- **Quản lý đơn hàng**: Theo dõi trạng thái đơn hàng
- **Hồ sơ người dùng**: Quản lý thông tin cá nhân, địa chỉ giao hàng

### Dành cho người bán
- **Bảng điều khiển**: Tổng quan về doanh số, đơn hàng, sản phẩm
- **Phân tích dữ liệu**: Biểu đồ doanh thu, sản phẩm bán chạy, tỷ lệ chuyển đổi
- **Quản lý sản phẩm**: Thêm, sửa, xóa sản phẩm
- **Quản lý kho hàng**: Theo dõi tồn kho, nhập hàng
- **Quản lý đơn hàng**: Xử lý đơn hàng, cập nhật trạng thái
- **Báo cáo**: Báo cáo doanh thu, sản phẩm, khách hàng

## 🚀 Công nghệ sử dụng

### Frontend
- **Next.js 15.1.0**: Framework React với App Router
- **React 18.3.1**: Thư viện UI
- **TypeScript**: Ngôn ngữ lập trình
- **Tailwind CSS**: Framework CSS
- **Shadcn UI**: Thư viện UI components
- **React Query**: Quản lý trạng thái và fetching data
- **Hookstate**: Quản lý state
- **i18next**: Đa ngôn ngữ
- **Chart.js & Recharts**: Biểu đồ và trực quan hóa dữ liệu
- **React Hook Form**: Quản lý form
- **Zod**: Validation schema

### Backend (Next.js API Routes)
- **MongoDB**: Cơ sở dữ liệu
- **Mongoose**: ODM cho MongoDB
- **NextAuth.js**: Xác thực người dùng
- **JWT**: Xác thực token
- **bcryptjs**: Mã hóa mật khẩu

### Công cụ phát triển
- **pnpm**: Package manager
- **ESLint & Prettier**: Linting và formatting
- **Husky**: Git hooks
- **Turbopack**: Bundler

## 📁 Cấu trúc dự án

```
zen-buy-fe/
├── locales/                # Các file ngôn ngữ (i18n)
├── public/                 # Tài nguyên tĩnh (hình ảnh, video)
├── scripts/                # Scripts hỗ trợ (seed data, migration)
├── src/
│   ├── app/                # App Router của Next.js
│   │   ├── [locale]/       # Định tuyến đa ngôn ngữ
│   │   ├── api/            # API Routes
│   │   ├── hooks/          # React hooks
│   │   ├── services/       # Services giao tiếp với API
│   │   └── store/          # State management
│   ├── components/         # React components
│   │   ├── landing/        # Components trang chủ
│   │   ├── layout/         # Components layout
│   │   ├── search/         # Components tìm kiếm
│   │   ├── seller/         # Components người bán
│   │   └── ui/             # UI components
│   ├── lib/                # Thư viện và utilities
│   ├── models/             # Mongoose models
│   ├── styles/             # CSS và SCSS
│   └── types/              # TypeScript types
├── .env.example            # Mẫu biến môi trường
├── .env.local              # Biến môi trường local
├── next.config.mjs         # Cấu hình Next.js
├── package.json            # Dependencies và scripts
└── tsconfig.json           # Cấu hình TypeScript
```

## 🔄 Luồng hoạt động

### Luồng người dùng
1. **Đăng ký/Đăng nhập**: Người dùng đăng ký tài khoản hoặc đăng nhập
2. **Duyệt sản phẩm**: Xem danh sách sản phẩm, tìm kiếm, lọc
3. **Xem chi tiết**: Xem thông tin chi tiết sản phẩm
4. **Thêm vào giỏ hàng**: Chọn sản phẩm và thêm vào giỏ hàng
5. **Thanh toán**: Nhập thông tin giao hàng và thanh toán
6. **Theo dõi đơn hàng**: Xem trạng thái đơn hàng
7. **Quản lý hồ sơ**: Cập nhật thông tin cá nhân, địa chỉ giao hàng
8. **Đánh giá sản phẩm**: Viết đánh giá và xếp hạng sản phẩm đã mua

### Luồng người bán
1. **Đăng ký/Đăng nhập**: Người bán đăng ký tài khoản hoặc đăng nhập
2. **Quản lý sản phẩm**: Thêm, sửa, xóa sản phẩm
3. **Quản lý kho hàng**: Theo dõi tồn kho, cập nhật số lượng
4. **Quản lý đơn hàng**: Xử lý đơn hàng, cập nhật trạng thái
5. **Xem phân tích**: Theo dõi doanh số, đơn hàng, sản phẩm bán chạy
6. **Quản lý cửa hàng**: Cập nhật thông tin cửa hàng, logo, banner
7. **Quản lý khuyến mãi**: Tạo và quản lý mã giảm giá, chương trình khuyến mãi

### Luồng dữ liệu
1. **Client Request**: Người dùng tương tác với UI
2. **React Query/Hooks**: Gọi API thông qua React Query
3. **Next.js API Routes**: Xử lý request
4. **MongoDB**: Lưu trữ và truy xuất dữ liệu
5. **Response**: Trả về dữ liệu cho client
6. **State Update**: Cập nhật state với Hookstate

## 🚧 Tính năng đang phát triển

Dự án đang trong quá trình phát triển và các tính năng sau đây sẽ được bổ sung trong tương lai:

### Dành cho người mua
- **Hồ sơ người dùng nâng cao**: Quản lý địa chỉ giao hàng, lịch sử đơn hàng
- **Đánh giá sản phẩm**: Hệ thống đánh giá và xếp hạng sản phẩm
- **Theo dõi đơn hàng chi tiết**: Xem chi tiết trạng thái đơn hàng theo thời gian thực
- **Tích hợp thanh toán**: Kết nối với các cổng thanh toán thực tế
- **Yêu thích sản phẩm**: Lưu sản phẩm yêu thích để mua sau

### Dành cho người bán
- **Quản lý đơn hàng nâng cao**: Bảng điều khiển quản lý đơn hàng toàn diện
- **Quản lý khách hàng**: Theo dõi và phân tích dữ liệu khách hàng
- **Báo cáo chi tiết**: Báo cáo doanh thu, sản phẩm theo nhiều tiêu chí
- **Quản lý khuyến mãi**: Tạo và quản lý mã giảm giá, chương trình khuyến mãi
- **Tích hợp vận chuyển**: Kết nối với các dịch vụ vận chuyển

## 🛠️ Cài đặt và chạy

### Yêu cầu
- Node.js (>= 18.18.0)
- pnpm
- MongoDB (local hoặc MongoDB Atlas)

### Cài đặt
1. Clone repository:
```bash
git clone https://github.com/your-username/zen-buy-fe.git
cd zen-buy-fe
```

2. Cài đặt dependencies:
```bash
pnpm install
```

3. Tạo file .env.local từ .env.example:
```bash
cp .env.example .env.local
```

4. Cập nhật các biến môi trường trong .env.local

5. Seed dữ liệu mẫu (tùy chọn):
```bash
pnpm run seed-rich-data
```

### Chạy ứng dụng
```bash
pnpm run dev
```

Ứng dụng sẽ chạy tại http://localhost:3001

## 🚢 Triển khai

Dự án được cấu hình để triển khai trên Vercel:

1. Đẩy code lên GitHub
2. Kết nối repository với Vercel
3. Cấu hình các biến môi trường trên Vercel
4. Vercel sẽ tự động build và deploy khi có commit mới

## 🌐 Đa ngôn ngữ

Dự án hỗ trợ đa ngôn ngữ với i18next:

- Tiếng Anh (mặc định)
- Tiếng Việt
- Tiếng Nhật
- Tiếng Trung

Các file ngôn ngữ được tổ chức theo trang và component trong thư mục `locales`.

## 👨‍💻 Tác giả

- **shadowhira** - [GitHub](https://github.com/shadowhira)

---

© 2024 ZenBuy. Bản quyền thuộc về shadowhira.
