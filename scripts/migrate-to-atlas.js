// Script to migrate data from local MongoDB to MongoDB Atlas
const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Create interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Configuration
const LOCAL_URI = 'mongodb://localhost:27017/ZenBuy';
const ATLAS_URI = process.env.MONGODB_URI;

// Collections to migrate
const COLLECTIONS = [
  'users',
  'products',
  'categories',
  'orders',
  'shops',
  // Add any other collections you need to migrate
];

async function migrateData() {
  console.log('Starting migration from local MongoDB to MongoDB Atlas...');
  
  // Connect to local MongoDB
  console.log('Connecting to local MongoDB...');
  const localClient = new MongoClient(LOCAL_URI);
  await localClient.connect();
  const localDb = localClient.db();
  console.log('Connected to local MongoDB');
  
  // Connect to MongoDB Atlas
  console.log('Connecting to MongoDB Atlas...');
  const atlasClient = new MongoClient(ATLAS_URI);
  await atlasClient.connect();
  const atlasDb = atlasClient.db();
  console.log('Connected to MongoDB Atlas');
  
  try {
    // Migrate each collection
    for (const collectionName of COLLECTIONS) {
      console.log(`Migrating collection: ${collectionName}`);
      
      // Get documents from local MongoDB
      const localCollection = localDb.collection(collectionName);
      const documents = await localCollection.find({}).toArray();
      
      if (documents.length === 0) {
        console.log(`No documents found in ${collectionName}, skipping...`);
        continue;
      }
      
      console.log(`Found ${documents.length} documents in ${collectionName}`);
      
      // Check if collection exists in Atlas and has documents
      const atlasCollection = atlasDb.collection(collectionName);
      const atlasCount = await atlasCollection.countDocuments();
      
      if (atlasCount > 0) {
        console.log(`Warning: Collection ${collectionName} in Atlas already has ${atlasCount} documents`);
        const answer = await askQuestion(`Do you want to drop the existing collection in Atlas before importing? (yes/no): `);
        
        if (answer.toLowerCase() === 'yes') {
          console.log(`Dropping collection ${collectionName} in Atlas...`);
          await atlasCollection.drop();
          console.log(`Collection ${collectionName} dropped`);
        } else {
          console.log(`Skipping collection ${collectionName}`);
          continue;
        }
      }
      
      // Insert documents into Atlas
      if (documents.length > 0) {
        console.log(`Inserting ${documents.length} documents into ${collectionName} in Atlas...`);
        const result = await atlasCollection.insertMany(documents);
        console.log(`Inserted ${result.insertedCount} documents into ${collectionName} in Atlas`);
      }
    }
    
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    // Close connections
    await localClient.close();
    await atlasClient.close();
    rl.close();
  }
}

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

// Run the migration
migrateData().catch(console.error);
