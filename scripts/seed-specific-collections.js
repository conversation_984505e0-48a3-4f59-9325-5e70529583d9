// Script to seed specific collections
require('dotenv').config({ path: '.env.local' });

const { MongoClient } = require('mongodb');
const { program } = require('commander');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'your_uri';
const MONGODB_DB = process.env.MONGODB_DB || 'ZenBuy';

// Define command line options
program
  .option('-c, --collections <collections>', 'Comma-separated list of collections to seed (e.g., "users,shops,categories,products")')
  .option('-f, --force', 'Force overwrite of existing data')
  .option('-l, --local-images', 'Use local fallback images instead of fetching from Unsplash')
  .option('-b, --batch-size <size>', 'Number of products to create in each batch (default: 20)', parseInt, 20)
  .parse(process.argv);

const options = program.opts();

// Main function to seed specific collections
async function seedSpecificCollections() {
  // Parse collections from command line
  const collectionsToSeed = options.collections ? options.collections.split(',') : [];

  if (collectionsToSeed.length === 0) {
    console.error('Error: No collections specified. Use --collections option.');
    console.log('Example: node scripts/seed-specific-collections.js --collections users,shops,categories,products');
    process.exit(1);
  }

  console.log(`Starting to seed collections: ${collectionsToSeed.join(', ')}...`);

  // Connect to MongoDB
  const client = new MongoClient(MONGODB_URI);
  await client.connect();
  console.log('Connected to MongoDB');

  const db = client.db(MONGODB_DB);

  try {
    // Import the seed-rich-data module
    const seedModule = require('./seed-rich-data');

    // Create a context object to pass to seed functions
    const context = {
      db,
      force: options.force || false,
      collections: collectionsToSeed,
      useUnsplash: !options.localImages,
      batchSize: options.batchSize || 20
    };

    // Call the appropriate seed functions based on collections
    if (collectionsToSeed.includes('users')) {
      await seedModule.seedUsers(context);
    }

    if (collectionsToSeed.includes('shops')) {
      await seedModule.seedShops(context);
    }

    if (collectionsToSeed.includes('categories')) {
      await seedModule.seedCategories(context);
    }

    if (collectionsToSeed.includes('products')) {
      await seedModule.seedProducts(context);
    }

    console.log('Specific collections seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding specific collections:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the seed function
seedSpecificCollections().catch(console.error);
