/**
 * Script để kiểm thử luồng quản lý sản phẩm và kho hàng
 *
 * <PERSON><PERSON>ch sử dụng:
 * 1. Đ<PERSON>m bảo server đang chạy (pnpm run dev)
 * 2. Chạy script: node scripts/test-product-inventory-flow.js
 *
 * Script này sẽ kiểm thử toàn bộ luồng quản lý sản phẩm và kho hàng:
 * - Tạo sản phẩm mới
 * - Cập nhật sản phẩm
 * - Nh<PERSON> bản sản phẩm
 * - Quản lý hình ảnh sản phẩm
 * - Cập nhật tồn kho
 * - Cài đặt ngưỡng tồn kho tối thiểu
 * - <PERSON><PERSON><PERSON> lịch sử nhập/xuất kho
 * - <PERSON><PERSON><PERSON> sản phẩm
 */

const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

// C<PERSON><PERSON> hình
const API_BASE_URL = 'http://localhost:3001/api';
let authToken = '';
let testProductId = '';
let duplicatedProductId = '';
let testImageUrl = 'https://via.placeholder.com/500x500?text=Test+Product';

// Tạo thư mục logs nếu chưa tồn tại
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// Tạo file log
const logFile = path.join(logsDir, `product-inventory-test-${new Date().toISOString().replace(/:/g, '-')}.log`);
fs.writeFileSync(logFile, `PRODUCT AND INVENTORY MANAGEMENT TEST LOG\n${new Date().toISOString()}\n\n`);

// Hàm ghi log
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  let consoleMessage;
  let fileMessage = `[${timestamp}] ${message}`;

  switch (type) {
    case 'success':
      consoleMessage = chalk.green(`✅ ${message}`);
      fileMessage = `[SUCCESS] ${fileMessage}`;
      break;
    case 'error':
      consoleMessage = chalk.red(`❌ ${message}`);
      fileMessage = `[ERROR] ${fileMessage}`;
      break;
    case 'warning':
      consoleMessage = chalk.yellow(`⚠️ ${message}`);
      fileMessage = `[WARNING] ${fileMessage}`;
      break;
    case 'info':
    default:
      consoleMessage = chalk.blue(`ℹ️ ${message}`);
      fileMessage = `[INFO] ${fileMessage}`;
      break;
  }

  console.log(consoleMessage);
  fs.appendFileSync(logFile, fileMessage + '\n');
}

// Hàm helper để gọi API
async function callApi(endpoint, method = 'GET', body = null) {
  const headers = {
    'Content-Type': 'application/json',
  };

  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`;
  }

  const options = {
    method,
    headers,
  };

  if (body && (method === 'POST' || method === 'PUT')) {
    options.body = JSON.stringify(body);
  }

  try {
    log(`Calling API: ${method} ${endpoint}`);
    const response = await fetch(`${API_BASE_URL}${endpoint}`, options);
    const data = await response.json();

    if (!response.ok) {
      log(`API Error: ${data.error || 'Unknown error'}`, 'error');
    }

    return {
      status: response.status,
      data,
    };
  } catch (error) {
    log(`Error calling API ${endpoint}: ${error.message}`, 'error');
    return {
      status: 500,
      data: { error: error.message },
    };
  }
}

// Đăng nhập để lấy token
async function login() {
  log('Đăng nhập để lấy token...');

  const response = await callApi('/auth/login', 'POST', {
    email: '<EMAIL>',
    password: 'password123',
  });

  if (response.status === 200 && response.data.token) {
    authToken = response.data.token;
    log('Đăng nhập thành công, đã lấy token', 'success');
    return true;
  } else {
    log('Đăng nhập thất bại: ' + (response.data.error || 'Unknown error'), 'error');
    log('Tiếp tục mà không có token (sử dụng auth-utils-temp)');
    return false;
  }
}

// Tạo sản phẩm mới
async function createProduct() {
  log('Tạo sản phẩm mới...');

  const product = {
    title: `Test Product ${Date.now()}`,
    description: '<p>This is a test product created by the automated test script</p><p>It includes <strong>rich text</strong> formatting.</p>',
    price: 99.99,
    stock: 100,
    specifications: {
      'Color': 'Black',
      'Weight': '200g',
      'Dimensions': '10 x 5 x 2 cm',
      'Material': 'Aluminum'
    },
    minStockThreshold: 10,
    images: [testImageUrl]
  };

  const response = await callApi('/seller/products', 'POST', product);

  if (response.status === 201 && response.data._id) {
    testProductId = response.data._id;
    log(`Tạo sản phẩm thành công, ID: ${testProductId}`, 'success');
    log(`Thông tin sản phẩm: ${JSON.stringify(response.data, null, 2)}`);
    return true;
  } else {
    log('Tạo sản phẩm thất bại: ' + (response.data.error || 'Unknown error'), 'error');
    return false;
  }
}

// Cập nhật sản phẩm
async function updateProduct() {
  if (!testProductId) {
    log('Không có ID sản phẩm để cập nhật', 'error');
    return false;
  }

  log(`Cập nhật sản phẩm ${testProductId}...`);

  const updatedData = {
    title: `Updated Test Product ${Date.now()}`,
    price: 129.99,
    specifications: {
      'Color': 'Silver',
      'Weight': '250g',
      'Dimensions': '10 x 5 x 2 cm',
      'Material': 'Aluminum',
      'Warranty': '1 year'
    }
  };

  const response = await callApi(`/seller/products/${testProductId}`, 'PUT', updatedData);

  if (response.status === 200) {
    log('Cập nhật sản phẩm thành công', 'success');
    log(`Thông tin sản phẩm sau khi cập nhật: ${JSON.stringify(response.data, null, 2)}`);
    return true;
  } else {
    log('Cập nhật sản phẩm thất bại: ' + (response.data.error || 'Unknown error'), 'error');
    return false;
  }
}

// Nhân bản sản phẩm
async function duplicateProduct() {
  if (!testProductId) {
    log('Không có ID sản phẩm để nhân bản', 'error');
    return false;
  }

  log(`Nhân bản sản phẩm ${testProductId}...`);

  const response = await callApi(`/seller/products/${testProductId}/duplicate`, 'POST');

  if (response.status === 201 && response.data._id) {
    duplicatedProductId = response.data._id;
    log(`Nhân bản sản phẩm thành công, ID mới: ${duplicatedProductId}`, 'success');
    log(`Thông tin sản phẩm nhân bản: ${JSON.stringify(response.data, null, 2)}`);
    return true;
  } else {
    log('Nhân bản sản phẩm thất bại: ' + (response.data.error || 'Unknown error'), 'error');
    return false;
  }
}

// Thêm hình ảnh sản phẩm
async function addProductImage() {
  if (!testProductId) {
    log('Không có ID sản phẩm để thêm hình ảnh', 'error');
    return false;
  }

  log(`Thêm hình ảnh cho sản phẩm ${testProductId}...`);

  const response = await callApi(`/seller/products/${testProductId}/images`, 'POST', {
    imageUrl: 'https://via.placeholder.com/500x500?text=Additional+Image'
  });

  if (response.status === 200) {
    log('Thêm hình ảnh sản phẩm thành công', 'success');
    log(`Danh sách hình ảnh: ${JSON.stringify(response.data.images, null, 2)}`);
    return true;
  } else {
    log('Thêm hình ảnh sản phẩm thất bại: ' + (response.data.error || 'Unknown error'), 'error');
    return false;
  }
}

// Cập nhật thứ tự hình ảnh
async function updateImageOrder() {
  if (!testProductId) {
    log('Không có ID sản phẩm để cập nhật thứ tự hình ảnh', 'error');
    return false;
  }

  // Lấy danh sách hình ảnh hiện tại
  const getResponse = await callApi(`/seller/products/${testProductId}`, 'GET');

  if (getResponse.status !== 200 || !getResponse.data.images) {
    log('Không thể lấy danh sách hình ảnh hiện tại', 'error');
    return false;
  }

  const currentImages = getResponse.data.images;

  if (currentImages.length < 2) {
    log('Cần ít nhất 2 hình ảnh để cập nhật thứ tự', 'warning');
    return false;
  }

  // Đảo ngược thứ tự hình ảnh
  const reversedImages = [...currentImages].reverse();

  log(`Cập nhật thứ tự hình ảnh cho sản phẩm ${testProductId}...`);

  const response = await callApi(`/seller/products/${testProductId}/images`, 'PUT', {
    images: reversedImages
  });

  if (response.status === 200) {
    log('Cập nhật thứ tự hình ảnh thành công', 'success');
    log(`Danh sách hình ảnh mới: ${JSON.stringify(response.data.images, null, 2)}`);
    return true;
  } else {
    log('Cập nhật thứ tự hình ảnh thất bại: ' + (response.data.error || 'Unknown error'), 'error');
    return false;
  }
}

// Cập nhật tồn kho
async function updateStock() {
  if (!testProductId) {
    log('Không có ID sản phẩm để cập nhật tồn kho', 'error');
    return false;
  }

  log(`Cập nhật tồn kho cho sản phẩm ${testProductId}...`);

  const response = await callApi('/seller/inventory/batch', 'POST', {
    items: [
      {
        productId: testProductId,
        quantity: 50,
        actionType: 'add',
        notes: 'Thêm hàng từ script test'
      }
    ]
  });

  if (response.status === 200 && response.data.success) {
    log('Cập nhật tồn kho thành công', 'success');
    log(`Kết quả: ${JSON.stringify(response.data.results, null, 2)}`);
    return true;
  } else {
    log('Cập nhật tồn kho thất bại: ' + (response.data.error || 'Unknown error'), 'error');
    return false;
  }
}

// Cài đặt ngưỡng tồn kho tối thiểu
async function setStockThreshold() {
  if (!testProductId) {
    log('Không có ID sản phẩm để cài đặt ngưỡng tồn kho', 'error');
    return false;
  }

  log(`Cài đặt ngưỡng tồn kho tối thiểu cho sản phẩm ${testProductId}...`);

  const response = await callApi(`/seller/products/${testProductId}/threshold`, 'PUT', {
    minStockThreshold: 25
  });

  if (response.status === 200 && response.data.success) {
    log('Cài đặt ngưỡng tồn kho tối thiểu thành công', 'success');
    log(`Thông tin ngưỡng: ${JSON.stringify(response.data, null, 2)}`);
    return true;
  } else {
    log('Cài đặt ngưỡng tồn kho tối thiểu thất bại: ' + (response.data.error || 'Unknown error'), 'error');
    return false;
  }
}

// Lấy lịch sử nhập/xuất kho
async function getInventoryHistory() {
  log('Lấy lịch sử nhập/xuất kho...');

  const response = await callApi('/seller/inventory/history', 'GET');

  if (response.status === 200) {
    log('Lấy lịch sử nhập/xuất kho thành công', 'success');
    log(`Số lượng bản ghi: ${response.data.history ? response.data.history.length : 0}`);
    if (response.data.history && response.data.history.length > 0) {
      log(`Bản ghi đầu tiên: ${JSON.stringify(response.data.history[0], null, 2)}`);
    }
    return true;
  } else {
    log('Lấy lịch sử nhập/xuất kho thất bại: ' + (response.data.error || 'Unknown error'), 'error');
    return false;
  }
}

// Xóa sản phẩm
async function deleteProduct(productId, productName = 'Test Product') {
  if (!productId) {
    log('Không có ID sản phẩm để xóa', 'error');
    return false;
  }

  log(`Xóa sản phẩm ${productId}...`);

  const response = await callApi(`/seller/products/${productId}`, 'DELETE');

  if (response.status === 200 && response.data.success) {
    log(`Xóa sản phẩm ${productName} thành công`, 'success');
    return true;
  } else {
    log(`Xóa sản phẩm ${productName} thất bại: ` + (response.data.error || 'Unknown error'), 'error');
    return false;
  }
}

// Tạo tài khoản seller nếu chưa tồn tại
async function createSellerAccount() {
  log('Tạo tài khoản seller...');

  // Tạo tài khoản seller mới
  const registerResponse = await callApi('/auth/register', 'POST', {
    name: 'Test Seller',
    email: '<EMAIL>',
    password: 'password123',
    role: 'seller'
  });

  if (registerResponse.status === 201 || registerResponse.status === 200) {
    log('Tạo tài khoản seller thành công', 'success');
    return true;
  } else {
    log('Tạo tài khoản seller thất bại: ' + (registerResponse.data.error || 'Unknown error'), 'error');

    // Nếu lỗi là do email đã tồn tại, coi như thành công
    if (registerResponse.data.error && registerResponse.data.error.includes('đã được sử dụng')) {
      log('Email đã tồn tại, tiếp tục với tài khoản hiện có', 'warning');
      return true;
    }

    return false;
  }
}

// Tạo shop cho seller nếu chưa tồn tại
async function createSellerShop() {
  log('Tạo shop cho seller...');

  // Tạo shop mới
  const createShopResponse = await callApi('/seller/shop/create', 'POST', {
    name: 'Test Shop',
    description: 'This is a test shop created by the automated test script',
    logo: 'https://via.placeholder.com/200x200?text=Test+Shop',
    banner: 'https://via.placeholder.com/1200x300?text=Test+Shop+Banner'
  });

  if (createShopResponse.status === 201 || createShopResponse.status === 200) {
    log('Tạo shop thành công', 'success');
    return true;
  } else {
    log('Tạo shop thất bại: ' + (createShopResponse.data.error || 'Unknown error'), 'error');

    // Nếu không có lỗi cụ thể, có thể shop đã tồn tại
    if (!createShopResponse.data.error) {
      log('Shop có thể đã tồn tại, tiếp tục với shop hiện có', 'warning');
      return true;
    }

    return false;
  }
}

// Chạy tất cả các test
async function runTests() {
  log('=== BẮT ĐẦU KIỂM THỬ LUỒNG QUẢN LÝ SẢN PHẨM VÀ KHO HÀNG ===', 'info');

  // Tạo tài khoản seller nếu chưa tồn tại
  await createSellerAccount();

  // Đăng nhập
  await login();

  // Tạo shop cho seller nếu chưa tồn tại
  await createSellerShop();

  // Tạo sản phẩm mới
  if (!await createProduct()) return;

  // Cập nhật sản phẩm
  await updateProduct();

  // Thêm hình ảnh sản phẩm
  await addProductImage();

  // Cập nhật thứ tự hình ảnh
  await updateImageOrder();

  // Nhân bản sản phẩm
  await duplicateProduct();

  // Cập nhật tồn kho
  await updateStock();

  // Cài đặt ngưỡng tồn kho tối thiểu
  await setStockThreshold();

  // Lấy lịch sử nhập/xuất kho
  await getInventoryHistory();

  // Xóa sản phẩm nhân bản
  if (duplicatedProductId) {
    await deleteProduct(duplicatedProductId, 'Duplicated Product');
  }

  // Xóa sản phẩm gốc
  await deleteProduct(testProductId, 'Original Product');

  log('=== KẾT THÚC KIỂM THỬ LUỒNG QUẢN LÝ SẢN PHẨM VÀ KHO HÀNG ===', 'info');
  log(`Kết quả kiểm thử đã được lưu vào file: ${logFile}`, 'info');
}

// Chạy các test
runTests();
