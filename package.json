{"name": "ecommerce.io", "version": "1.0.0", "private": true, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/shadowhira"}, "scripts": {"translate": "i18nexus pull", "dev": "next dev -p 3001", "clean": "rm -rf node_modules pnpm-lock.yaml && pnpm store prune", "reinstall": "pnpm run clean && pnpm install", "prebuild": "pnpm install --no-frozen-lockfile", "build": "next build", "build:vercel": "HUSKY=0 next build", "start": "next start", "migrate-to-atlas": "node scripts/migrate-to-atlas.js", "seed-rich-data": "node scripts/seed-rich-data.js", "seed-collections": "node scripts/seed-specific-collections.js", "test:product-apis": "node scripts/test-product-apis.js", "test:product-inventory": "node scripts/test-product-inventory-flow.js", "lint": "next lint", "lint:fix": "eslint src --fix && pnpm format", "lint:strict": "eslint --max-warnings=0 src", "format": "prettier --write .", "format:check": "prettier -c -w .", "prepare": "node -e \"try{require('./scripts/prepare.js')}catch(e){}\""}, "lint-staged": {"**/*.{js,jsx,tsx,ts,css,less,scss,sass}": ["prettier --write --no-error-on-unmatched-pattern"]}, "dependencies": {"@asteasolutions/zod-to-openapi": "^7.3.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@faker-js/faker": "^9.6.0", "@hookform/resolvers": "^3.10.0", "@hookstate/core": "^4.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.67.2", "@tanstack/react-query-devtools": "^5.67.2", "@tanstack/react-table": "^8.21.2", "@types/nodemailer": "^6.4.17", "@types/seedrandom": "^3.0.8", "@typescript-eslint/parser": "^8.26.0", "@zxing/library": "^0.21.3", "autoprefixer": "10.4.20", "bcryptjs": "^3.0.2", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "embla-carousel-react": "8.5.1", "eslint": "9.22.0", "eslint-config-next": "15.1.0", "i18next": "^24.2.2", "i18next-resources-to-backend": "^1.2.1", "i18nexus-cli": "^3.5.0", "input-otp": "1.4.1", "jsbarcode": "^3.11.6", "jsonwebtoken": "^9.0.2", "kbar": "0.1.0-beta.45", "lucide-react": "^0.468.0", "match-sorter": "^8.0.0", "mongodb": "^6.15.0", "mongoose": "^8.13.1", "motion": "^11.18.2", "next": "15.1.0", "next-auth": "5.0.0-beta.25", "next-i18n-router": "^5.5.1", "next-i18next": "^15.4.2", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "nodemailer": "^7.0.3", "nuqs": "^2.4.1", "postcss": "8.4.49", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "18.3.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.5.1", "react-dom": "18.3.1", "react-dropzone": "^14.3.8", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.1", "react-resizable-panels": "^2.1.7", "react-responsive": "^10.0.1", "react-to-print": "^3.1.0", "react-virtual": "^2.10.4", "recharts": "^2.15.1", "seedrandom": "^3.0.5", "sharp": "^0.33.5", "sonner": "^1.7.4", "sort-by": "^1.2.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.2", "uuid": "^11.1.0", "vanilla-tilt": "^1.8.1", "vaul": "^0.3.7", "zod": "^3.24.2"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "@types/node": "22.13.10", "@types/react": "^18.3.18", "@types/react-dom": "18.0.2", "@types/sort-by": "^1.2.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.26.0", "chalk": "4", "commander": "^13.1.0", "dotenv": "^16.5.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "node-fetch": "2", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "sass": "^1.85.1", "slugify": "^1.6.6"}}