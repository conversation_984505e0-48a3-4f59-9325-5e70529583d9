{"compilerOptions": {"forceConsistentCasingInFileNames": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "target": "ES6", "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": "./", "paths": {"@/*": ["src/*"], "@app/*": ["src/app/*"], "@components/*": ["src/components/*"], "@utils/*": ["src/utils/*"], "@lib/*": ["src/lib/*"], "@styles/*": ["src/styles/*"], "@images/*": ["public/images/*"], "@types/*": ["src/types/*"], "@svg/*": ["public/svg/*"], "@videos/*": ["public/videos/*"], "@animations/*": ["src/styles/animations/*"], "@hooks/*": ["src/app/hooks/*"], "@services/*": ["src/app/services/*"], "@store/*": ["src/app/store/*"], "@apis/*": ["src/apis/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}